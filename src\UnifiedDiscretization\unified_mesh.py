"""
统一网格抽象层 - 支持结构网格和非结构网格的统一处理
"""
import torch
from abc import ABC, abstractmethod
from enum import Enum
from typing import Tuple, Dict, Optional, Union
import numpy as np

class MeshType(Enum):
    STRUCTURED = "structured"
    UNSTRUCTURED = "unstructured"

class DiscretizationMethod(Enum):
    FINITE_DIFFERENCE = "finite_difference"
    FINITE_VOLUME = "finite_volume"

class UnifiedMeshBase(ABC):
    """统一网格基类"""
    
    def __init__(self, mesh_type: MeshType, discretization: DiscretizationMethod):
        self.mesh_type = mesh_type
        self.discretization = discretization
        
        # 统一数据结构
        self.cells_connectivity = None    # 单元连接关系
        self.face_connectivity = None     # 面连接关系  
        self.neighbor_mapping = None      # 邻居映射关系
        self.geometric_metrics = None     # 几何度量信息
        
    @abstractmethod
    def build_connectivity(self):
        """构建连接关系"""
        pass
    
    @abstractmethod
    def compute_geometric_metrics(self):
        """计算几何度量"""
        pass
    
    @abstractmethod
    def get_neighbor_cells(self, cell_id: int):
        """获取邻居单元"""
        pass
    
    @abstractmethod
    def get_cell_faces(self, cell_id: int):
        """获取单元的面"""
        pass

class StructuredMesh(UnifiedMeshBase):
    """结构网格实现"""
    
    def __init__(self, ni: int, nj: int, dx: float, dy: float):
        super().__init__(MeshType.STRUCTURED, DiscretizationMethod.FINITE_DIFFERENCE)
        self.ni, self.nj = ni, nj
        self.dx, self.dy = dx, dy
        self.total_cells = ni * nj
        
        self.build_connectivity()
        self.compute_geometric_metrics()
    
    def build_connectivity(self):
        """构建结构网格的连接关系"""
        # 将二维索引(i,j)映射为一维索引
        def ij_to_index(i, j):
            return i * self.nj + j
        
        # 构建邻居映射 - 结构网格的规律性
        neighbor_list = []
        face_list = []
        
        for i in range(self.ni):
            for j in range(self.nj):
                cell_id = ij_to_index(i, j)
                neighbors = []
                faces = []
                
                # 东邻居 (i+1, j)
                if i < self.ni - 1:
                    neighbors.append(ij_to_index(i+1, j))
                    faces.append(('east', cell_id, ij_to_index(i+1, j)))
                
                # 西邻居 (i-1, j)  
                if i > 0:
                    neighbors.append(ij_to_index(i-1, j))
                    faces.append(('west', ij_to_index(i-1, j), cell_id))
                
                # 北邻居 (i, j+1)
                if j < self.nj - 1:
                    neighbors.append(ij_to_index(i, j+1))
                    faces.append(('north', cell_id, ij_to_index(i, j+1)))
                
                # 南邻居 (i, j-1)
                if j > 0:
                    neighbors.append(ij_to_index(i, j-1))
                    faces.append(('south', ij_to_index(i, j-1), cell_id))
                
                neighbor_list.append(neighbors)
                face_list.extend(faces)
        
        self.neighbor_mapping = neighbor_list
        self.face_connectivity = face_list
        
        # 转换为统一的张量格式
        self._convert_to_unified_format()
    
    def _convert_to_unified_format(self):
        """转换为与非结构网格兼容的格式"""
        # 构建类似非结构网格的 cells_face, cells_index
        cells_face_list = []
        cells_index_list = []
        
        face_id = 0
        for cell_id, neighbors in enumerate(self.neighbor_mapping):
            for neighbor_id in neighbors:
                cells_face_list.append(face_id)
                cells_index_list.append(cell_id)
                face_id += 1
        
        self.cells_face = torch.tensor(cells_face_list, dtype=torch.long)
        self.cells_index = torch.tensor(cells_index_list, dtype=torch.long)
    
    def compute_geometric_metrics(self):
        """计算几何度量"""
        self.geometric_metrics = {
            'cell_volume': torch.full((self.total_cells,), self.dx * self.dy),
            'face_area_x': torch.full((self.total_cells,), self.dy),  # x方向面积
            'face_area_y': torch.full((self.total_cells,), self.dx),  # y方向面积
            'dx': self.dx,
            'dy': self.dy
        }
    
    def get_neighbor_cells(self, cell_id: int):
        """获取邻居单元"""
        return self.neighbor_mapping[cell_id]
    
    def get_cell_faces(self, cell_id: int):
        """获取单元的面"""
        i, j = divmod(cell_id, self.nj)
        faces = []
        
        # 根据位置确定面
        if i < self.ni - 1:  # 东面
            faces.append(('east', self.dy))
        if i > 0:  # 西面
            faces.append(('west', self.dy))
        if j < self.nj - 1:  # 北面
            faces.append(('north', self.dx))
        if j > 0:  # 南面
            faces.append(('south', self.dx))
            
        return faces

class UnstructuredMesh(UnifiedMeshBase):
    """非结构网格实现"""
    
    def __init__(self, cells_node, cells_face, cells_index, face_node, 
                 mesh_pos, face_area, cells_area):
        super().__init__(MeshType.UNSTRUCTURED, DiscretizationMethod.FINITE_VOLUME)
        
        # 直接使用现有的非结构网格数据
        self.cells_node = cells_node
        self.cells_face = cells_face
        self.cells_index = cells_index
        self.face_node = face_node
        self.mesh_pos = mesh_pos
        self.face_area = face_area
        self.cells_area = cells_area
        
        self.build_connectivity()
        self.compute_geometric_metrics()
    
    def build_connectivity(self):
        """构建非结构网格的连接关系"""
        # 非结构网格已有完整的连接信息
        self.cells_connectivity = {
            'cells_face': self.cells_face,
            'cells_index': self.cells_index,
            'face_node': self.face_node
        }
        
        self.face_connectivity = self.face_node
        
        # 构建邻居映射
        self._build_neighbor_mapping()
    
    def _build_neighbor_mapping(self):
        """构建邻居映射关系"""
        from torch_scatter import scatter
        
        # 通过面连接关系构建邻居映射
        neighbor_cells = scatter(
            src=self.cells_index.unsqueeze(1).float(),
            index=self.cells_face,
            dim=0,
            reduce='mean'  # 这里需要更复杂的逻辑来正确处理
        )
        
        self.neighbor_mapping = neighbor_cells
    
    def compute_geometric_metrics(self):
        """计算几何度量"""
        self.geometric_metrics = {
            'cell_volume': self.cells_area,
            'face_area': self.face_area,
            'mesh_pos': self.mesh_pos
        }
    
    def get_neighbor_cells(self, cell_id: int):
        """获取邻居单元"""
        # 通过面连接关系找邻居
        cell_faces = (self.cells_index == cell_id).nonzero().squeeze()
        neighbor_faces = self.cells_face[cell_faces]
        
        # 找到共享这些面的其他单元
        neighbors = []
        for face_id in neighbor_faces:
            face_cells = (self.cells_face == face_id).nonzero().squeeze()
            face_cell_ids = self.cells_index[face_cells]
            neighbors.extend([cid.item() for cid in face_cell_ids if cid != cell_id])
        
        return list(set(neighbors))
    
    def get_cell_faces(self, cell_id: int):
        """获取单元的面"""
        cell_faces = (self.cells_index == cell_id).nonzero().squeeze()
        return self.cells_face[cell_faces]

class MeshFactory:
    """网格工厂类"""
    
    @staticmethod
    def create_structured_mesh(ni: int, nj: int, dx: float, dy: float) -> StructuredMesh:
        """创建结构网格"""
        return StructuredMesh(ni, nj, dx, dy)
    
    @staticmethod
    def create_unstructured_mesh(cells_node, cells_face, cells_index, 
                               face_node, mesh_pos, face_area, cells_area) -> UnstructuredMesh:
        """创建非结构网格"""
        return UnstructuredMesh(cells_node, cells_face, cells_index, 
                              face_node, mesh_pos, face_area, cells_area)
    
    @staticmethod
    def auto_detect_mesh_type(mesh_data: Dict) -> MeshType:
        """自动检测网格类型"""
        if 'ni' in mesh_data and 'nj' in mesh_data:
            return MeshType.STRUCTURED
        elif 'cells_face' in mesh_data and 'face_node' in mesh_data:
            return MeshType.UNSTRUCTURED
        else:
            raise ValueError("无法识别网格类型")
