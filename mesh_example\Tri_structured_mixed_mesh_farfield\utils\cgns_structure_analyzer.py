#!/usr/bin/env python3
"""
CGNS Structure Analyzer

This script analyzes CGNS files and exports their hierarchical structure to JSON format.
It preserves the tree structure while replacing large data arrays with shape information.

Usage:
    python cgns_structure_analyzer.py <cgns_file> [output_json]
"""

import sys
import json
import numpy as np
from pathlib import Path

try:
    import CGNS.PAT.cgnslib as cgnslib
    import CGNS.PAT.cgnskeywords as cgnskeywords
    HAS_PYCGNS = True
except ImportError:
    HAS_PYCGNS = False

try:
    import h5py
    HAS_H5PY = True
except ImportError:
    HAS_H5PY = False


class CGNSStructureAnalyzer:
    """Analyzes CGNS file structure and exports to JSON"""
    
    def __init__(self, cgns_file_path):
        self.cgns_file_path = Path(cgns_file_path)
        self.structure = {}
        
    def analyze_with_pycgns(self):
        """Analyze CGNS structure using pyCGNS library"""
        if not HAS_PYCGNS:
            raise ImportError("pyCGNS library not available")
            
        # Load CGNS tree
        tree = cgnslib.load(str(self.cgns_file_path))
        
        # Convert tree to structured format
        self.structure = self._convert_cgns_tree(tree)
        
        return self.structure
    
    def analyze_with_h5py(self):
        """Analyze CGNS structure using h5py (for HDF5-based CGNS files)"""
        if not HAS_H5PY:
            raise ImportError("h5py library not available")
            
        with h5py.File(self.cgns_file_path, 'r') as f:
            self.structure = self._convert_h5_group(f, "root")
            
        return self.structure
    
    def _convert_cgns_tree(self, node, level=0):
        """Convert CGNS tree node to structured dictionary"""
        if node is None:
            return None
            
        # CGNS node structure: [name, value, children, label]
        name = node[0] if len(node) > 0 else "unnamed"
        value = node[1] if len(node) > 1 else None
        children = node[2] if len(node) > 2 else []
        label = node[3] if len(node) > 3 else "unknown"
        
        result = {
            "name": name,
            "label": label,
            "type": type(value).__name__ if value is not None else "None"
        }
        
        # Handle value based on type
        if value is not None:
            if isinstance(value, np.ndarray):
                result["data_type"] = str(value.dtype)
                result["shape"] = list(value.shape)
                result["size"] = int(value.size)
                
                # For small arrays or important metadata, include actual values
                should_show_value = (
                    (value.size <= 10 and not self._is_coordinate_data(name, label)) or
                    (value.size <= 100 and self._is_important_metadata(name, label))
                )

                if should_show_value:
                    try:
                        if value.dtype.kind in ['U', 'S']:  # String types
                            result["value"] = self._convert_value(value.tolist())
                        elif value.dtype.kind in ['i', 'f']:  # Numeric types
                            result["value"] = self._convert_value(value.tolist())
                    except:
                        result["value"] = "conversion_failed"
                else:
                    result["value"] = f"<array_shape_{value.shape}>"
            else:
                result["value"] = self._convert_value(value)
        
        # Process children
        if children:
            result["children"] = {}
            for child in children:
                child_name = child[0] if child else "unnamed_child"
                result["children"][child_name] = self._convert_cgns_tree(child, level + 1)
                
        return result
    
    def _convert_h5_group(self, group, name):
        """Convert HDF5 group to structured dictionary"""
        result = {
            "name": name,
            "type": "Group" if isinstance(group, h5py.Group) else "Dataset"
        }
        
        # Add attributes
        if group.attrs:
            result["attributes"] = {}
            for attr_name, attr_value in group.attrs.items():
                if isinstance(attr_value, np.ndarray):
                    if attr_value.size <= 10:
                        result["attributes"][attr_name] = self._convert_value(attr_value.tolist())
                    else:
                        result["attributes"][attr_name] = f"<array_shape_{attr_value.shape}>"
                else:
                    result["attributes"][attr_name] = self._convert_value(attr_value)
        
        if isinstance(group, h5py.Group):
            # Process group children
            result["children"] = {}
            for key in group.keys():
                child = group[key]
                result["children"][key] = self._convert_h5_group(child, key)
        else:
            # Process dataset
            dataset = group
            result["data_type"] = str(dataset.dtype)
            result["shape"] = list(dataset.shape)
            result["size"] = int(dataset.size)
            
            # For small datasets or important metadata, include actual values
            should_show_value = (
                (dataset.size <= 10 and not self._is_coordinate_data(name, "")) or
                (dataset.size <= 100 and self._is_important_metadata(name, ""))
            )

            if should_show_value:
                try:
                    result["value"] = self._convert_value(dataset[:].tolist())
                except:
                    result["value"] = f"<dataset_shape_{dataset.shape}>"
            else:
                result["value"] = f"<dataset_shape_{dataset.shape}>"
                
        return result
    
    def _convert_value(self, value):
        """Convert value to JSON-serializable format"""
        if isinstance(value, (bytes, np.bytes_)):
            try:
                return value.decode('utf-8')
            except UnicodeDecodeError:
                return f"<bytes_length_{len(value)}>"
        elif isinstance(value, np.str_):
            return str(value)
        elif isinstance(value, list):
            # Check if this looks like ASCII character codes
            if (len(value) > 0 and
                all(isinstance(x, (int, np.integer)) for x in value) and
                all(0 <= x <= 127 for x in value)):
                try:
                    ascii_str = ''.join(chr(x) for x in value if x != 0)  # Remove null terminators
                    if ascii_str.isprintable():
                        return {"ascii_decoded": ascii_str, "raw_values": value}
                except:
                    pass
            return [self._convert_value(item) for item in value]
        elif isinstance(value, np.ndarray):
            return [self._convert_value(item) for item in value.tolist()]
        else:
            return value

    def _is_coordinate_data(self, name, label):
        """Check if this is coordinate data that should not be fully printed"""
        coordinate_indicators = [
            'CoordinateX', 'CoordinateY', 'CoordinateZ',
            'coordinate', 'coord', 'position', 'pos',
            'GridCoordinates', 'ElementConnectivity'
        ]

        name_lower = name.lower()
        label_lower = label.lower()

        return any(indicator.lower() in name_lower or indicator.lower() in label_lower
                  for indicator in coordinate_indicators)

    def _is_important_metadata(self, name, label):
        """Check if this is important metadata that should be displayed even if larger"""
        important_indicators = [
            'ZoneType', 'FamilyName', 'GridLocation', 'Transform',
            'ElementRange', 'PointRange', 'data'
        ]

        name_lower = name.lower()
        label_lower = label.lower()

        return any(indicator.lower() in name_lower or indicator.lower() in label_lower
                  for indicator in important_indicators)
    
    def analyze(self):
        """Analyze CGNS file structure using available libraries"""
        try:
            # Try pyCGNS first (more comprehensive for CGNS)
            return self.analyze_with_pycgns()
        except (ImportError, Exception) as e:
            print(f"pyCGNS analysis failed: {e}")
            try:
                # Fallback to h5py for HDF5-based CGNS files
                return self.analyze_with_h5py()
            except (ImportError, Exception) as e2:
                print(f"h5py analysis also failed: {e2}")
                raise RuntimeError("Could not analyze CGNS file with available libraries")
    
    def save_to_json(self, output_path=None):
        """Save only summary to JSON file"""
        if not self.structure:
            self.analyze()

        if output_path is None:
            output_path = self.cgns_file_path.with_suffix('.summary.json')

        # Create summary-only output structure
        output_data = {
            "file_info": {
                "file_path": str(self.cgns_file_path),
                "file_size_bytes": self.cgns_file_path.stat().st_size if self.cgns_file_path.exists() else 0,
                "analysis_timestamp": json.dumps({"timestamp": "generated_at_runtime"})
            },
            "summary": self._generate_summary()
        }

        # Custom JSON encoder for numpy types and bytes
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, (bytes, np.bytes_)):
                    try:
                        return obj.decode('utf-8')
                    except UnicodeDecodeError:
                        return f"<bytes_length_{len(obj)}>"
                elif isinstance(obj, np.str_):
                    return str(obj)
                return super().default(obj)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, cls=NumpyEncoder, ensure_ascii=False)

        print(f"CGNS summary saved to: {output_path}")
        return output_path
    
    def print_summary(self):
        """Print a summary of the CGNS structure"""
        if not self.structure:
            self.analyze()
            
        print(f"\n=== CGNS File Structure Summary ===")
        print(f"File: {self.cgns_file_path}")
        
        self._print_node_summary(self.structure, level=0)
    
    def _generate_summary(self):
        """Generate a summary of the CGNS structure"""
        summary = {
            "zones": {},
            "boundary_conditions": {},
            "grid_connectivity": {},
            "families": {}
        }

        if not self.structure:
            return summary

        # Extract zone information
        base_children = self.structure.get("children", {})
        for base_name, base_node in base_children.items():
            if base_node.get("type") == "Group":
                zone_children = base_node.get("children", {})
                for zone_name, zone_node in zone_children.items():
                    if zone_name.startswith("dom-"):
                        zone_info = self._extract_zone_info(zone_node)
                        if zone_info:
                            summary["zones"][zone_name] = zone_info
                    elif zone_name in ["inlet", "outlet", "wall"]:
                        family_info = self._extract_family_info(zone_node)
                        if family_info:
                            summary["families"][zone_name] = family_info

        return summary

    def _extract_zone_info(self, zone_node):
        """Extract zone information"""
        info = {}
        children = zone_node.get("children", {})

        # Zone type
        if "ZoneType" in children:
            zone_type_children = children["ZoneType"].get("children", {})
            if " data" in zone_type_children:
                zone_type_data = zone_type_children[" data"]
                if "value" in zone_type_data and isinstance(zone_type_data["value"], dict):
                    info["zone_type"] = zone_type_data["value"].get("ascii_decoded", "unknown")

        # Zone dimensions from zone data
        if " data" in children:
            data_info = children[" data"]
            if "shape" in data_info:
                info["zone_dimensions"] = data_info["shape"]

        # Grid coordinates info
        if "GridCoordinates" in children:
            coord_children = children["GridCoordinates"].get("children", {})
            coord_info = {}
            for coord_name in ["CoordinateX", "CoordinateY", "CoordinateZ"]:
                if coord_name in coord_children:
                    coord_data_children = coord_children[coord_name].get("children", {})
                    if " data" in coord_data_children:
                        coord_data = coord_data_children[" data"]
                        if "shape" in coord_data:
                            coord_info[coord_name] = {
                                "shape": coord_data["shape"],
                                "data_type": coord_data.get("data_type", "unknown")
                            }
            if coord_info:
                info["coordinates"] = coord_info

        # Boundary conditions with detailed analysis
        if "ZoneBC" in children:
            bc_children = children["ZoneBC"].get("children", {})
            bc_info = {}
            for bc_name, bc_node in bc_children.items():
                bc_detail = self._extract_boundary_condition_info(bc_node)
                if bc_detail:
                    bc_info[bc_name] = bc_detail
            if bc_info:
                info["boundary_conditions"] = bc_info

        # Grid connectivity
        if "ZoneGridConnectivity" in children:
            conn_children = children["ZoneGridConnectivity"].get("children", {})
            info["grid_connectivity"] = list(conn_children.keys())

        return info

    def _extract_boundary_condition_info(self, bc_node):
        """Extract detailed boundary condition information"""
        bc_info = {}
        children = bc_node.get("children", {})

        # Grid location (Vertex, EdgeCenter, FaceCenter, etc.)
        if "GridLocation" in children:
            grid_loc_children = children["GridLocation"].get("children", {})
            if " data" in grid_loc_children:
                grid_loc_data = grid_loc_children[" data"]
                if "value" in grid_loc_data and isinstance(grid_loc_data["value"], dict):
                    bc_info["grid_location"] = grid_loc_data["value"].get("ascii_decoded", "unknown")

        # Point range (indices of vertices/faces/edges)
        if "PointRange" in children:
            point_range_children = children["PointRange"].get("children", {})
            if " data" in point_range_children:
                point_range_data = point_range_children[" data"]
                if "value" in point_range_data:
                    bc_info["point_range"] = point_range_data["value"]
                    bc_info["point_range_shape"] = point_range_data.get("shape", [])

        # Family name (to link with BC type)
        if "FamilyName" in children:
            family_name_children = children["FamilyName"].get("children", {})
            if " data" in family_name_children:
                family_name_data = family_name_children[" data"]
                if "value" in family_name_data and isinstance(family_name_data["value"], dict):
                    bc_info["family_name"] = family_name_data["value"].get("ascii_decoded", "unknown")

        return bc_info

    def _extract_family_info(self, family_node):
        """Extract family information based on BC types like BCInflow, BCOutflow, BCWall"""
        info = {}
        children = family_node.get("children", {})

        # Family BC type (BCInflow, BCOutflow, BCWall, etc.)
        if "FamBC" in children:
            fambc_children = children["FamBC"].get("children", {})
            if " data" in fambc_children:
                fambc_data = fambc_children[" data"]
                if "value" in fambc_data and isinstance(fambc_data["value"], dict):
                    bc_type = fambc_data["value"].get("ascii_decoded", "unknown")
                    info["bc_type"] = bc_type

                    # Check if this is one of the standard BC types we're interested in
                    standard_bc_types = ["BCInflow", "BCOutflow", "BCWall"]
                    if bc_type in standard_bc_types:
                        info["is_standard_bc"] = True
                    else:
                        info["is_standard_bc"] = False

        # Family BC type name
        if "FamBC_TypeName" in children:
            type_name_children = children["FamBC_TypeName"].get("children", {})
            if " data" in type_name_children:
                type_name_data = type_name_children[" data"]
                if "value" in type_name_data and isinstance(type_name_data["value"], dict):
                    info["bc_type_name"] = type_name_data["value"].get("ascii_decoded", "unknown")

        return info

    def _print_node_summary(self, node, level=0):
        """Print summary of a node and its children"""
        indent = "  " * level
        name = node.get("name", "unnamed")
        label = node.get("label", node.get("type", "unknown"))

        if "shape" in node:
            shape_info = f" (shape: {node['shape']})"
        else:
            shape_info = ""

        print(f"{indent}{name} [{label}]{shape_info}")

        # Print children
        children = node.get("children", {})
        for child_node in children.values():
            self._print_node_summary(child_node, level + 1)


def main():
    """Main function for command line usage"""
    # Default CGNS file if no arguments provided
    default_cgns_file = "mesh_example/Tri_structured_mixed_mesh_farfield/Tri_structured_mixed_mesh_farfield.cgns"

    if len(sys.argv) < 2:
        # Use default file if it exists
        if Path(default_cgns_file).exists():
            cgns_file = default_cgns_file
            output_file = None
            print(f"No arguments provided, using default file: {cgns_file}")
        else:
            print("Usage: python cgns_structure_analyzer.py <cgns_file> [output_json]")
            print(f"Default file not found: {default_cgns_file}")
            sys.exit(1)
    else:
        cgns_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None

    if not Path(cgns_file).exists():
        print(f"Error: CGNS file not found: {cgns_file}")
        sys.exit(1)

    try:
        analyzer = CGNSStructureAnalyzer(cgns_file)

        # Analyze and print summary
        analyzer.print_summary()

        # Save to JSON
        output_path = analyzer.save_to_json(output_file)

        print(f"\nAnalysis complete!")
        print(f"Structure saved to: {output_path}")

    except Exception as e:
        print(f"Error analyzing CGNS file: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
