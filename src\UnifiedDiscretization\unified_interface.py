"""
统一接口 - 为用户提供简单统一的调用接口
"""
import torch
from typing import Dict, Any, Optional, Union
from .unified_mesh import MeshFactory, MeshType, UnifiedMeshBase
from .unified_discretization import UnifiedSolver

class UnifiedCFDSolver:
    """统一CFD求解器接口"""
    
    def __init__(self, mesh_config: Dict[str, Any]):
        """
        初始化统一求解器
        
        Args:
            mesh_config: 网格配置字典
                结构网格: {'type': 'structured', 'ni': 100, 'nj': 50, 'dx': 0.01, 'dy': 0.01}
                非结构网格: {'type': 'unstructured', 'cells_node': ..., 'cells_face': ..., ...}
        """
        self.mesh_config = mesh_config
        self.mesh = self._create_mesh(mesh_config)
        self.solver = UnifiedSolver(self.mesh)
        
        # 记录求解器信息
        self.solver_info = self.solver.get_method_info()
        print(f"初始化求解器: {self.solver_info}")
    
    def _create_mesh(self, config: Dict[str, Any]) -> UnifiedMeshBase:
        """根据配置创建网格"""
        mesh_type = config.get('type', 'auto')
        
        if mesh_type == 'structured':
            return MeshFactory.create_structured_mesh(
                ni=config['ni'],
                nj=config['nj'], 
                dx=config['dx'],
                dy=config['dy']
            )
        elif mesh_type == 'unstructured':
            return MeshFactory.create_unstructured_mesh(
                cells_node=config['cells_node'],
                cells_face=config['cells_face'],
                cells_index=config['cells_index'],
                face_node=config['face_node'],
                mesh_pos=config['mesh_pos'],
                face_area=config['face_area'],
                cells_area=config['cells_area']
            )
        elif mesh_type == 'auto':
            detected_type = MeshFactory.auto_detect_mesh_type(config)
            config['type'] = detected_type.value
            return self._create_mesh(config)
        else:
            raise ValueError(f"不支持的网格类型: {mesh_type}")
    
    def solve_step(self, Q: torch.Tensor) -> torch.Tensor:
        """执行一步求解"""
        return self.solver.solve_euler_equations(Q)
    
    def solve_steady(self, Q_init: torch.Tensor, max_iter: int = 1000, 
                    tolerance: float = 1e-6) -> torch.Tensor:
        """稳态求解"""
        Q = Q_init.clone()
        
        for iter_count in range(max_iter):
            Q_old = Q.clone()
            Q = self.solve_step(Q)
            
            # 检查收敛性
            residual_norm = torch.norm(Q - Q_old)
            if residual_norm < tolerance:
                print(f"收敛于第 {iter_count} 步, 残差: {residual_norm:.2e}")
                break
                
            if iter_count % 100 == 0:
                print(f"第 {iter_count} 步, 残差: {residual_norm:.2e}")
        
        return Q
    
    def get_mesh_info(self) -> Dict[str, Any]:
        """获取网格信息"""
        return {
            'mesh_type': self.mesh.mesh_type.value,
            'discretization_method': self.mesh.discretization.value,
            'total_cells': getattr(self.mesh, 'total_cells', 
                                 len(self.mesh.geometric_metrics.get('cell_volume', []))),
            'geometric_metrics': self.mesh.geometric_metrics
        }

def create_structured_solver(ni: int, nj: int, dx: float, dy: float) -> UnifiedCFDSolver:
    """创建结构网格求解器的便捷函数"""
    config = {
        'type': 'structured',
        'ni': ni,
        'nj': nj,
        'dx': dx,
        'dy': dy
    }
    return UnifiedCFDSolver(config)

def create_unstructured_solver(mesh_data: Dict[str, torch.Tensor]) -> UnifiedCFDSolver:
    """创建非结构网格求解器的便捷函数"""
    config = {
        'type': 'unstructured',
        **mesh_data
    }
    return UnifiedCFDSolver(config)

def load_solver_from_existing_project(graph_node, graph_edge, graph_cell) -> UnifiedCFDSolver:
    """从现有项目数据创建求解器"""
    # 提取现有项目的网格数据
    mesh_data = {
        'cells_node': graph_node.face,
        'cells_face': graph_edge.face,
        'cells_index': graph_cell.face,
        'face_node': graph_edge.edge_index,
        'mesh_pos': graph_node.pos,
        'face_area': graph_edge.face_area,
        'cells_area': graph_cell.cells_area
    }
    
    return create_unstructured_solver(mesh_data)

# 使用示例和测试函数
def example_structured_usage():
    """结构网格使用示例"""
    print("=== 结构网格求解示例 ===")
    
    # 创建结构网格求解器
    solver = create_structured_solver(ni=50, nj=30, dx=0.02, dy=0.02)
    
    # 初始化流场
    total_cells = 50 * 30
    Q_init = torch.ones(total_cells, 4)  # [rho, rho*u, rho*v, rho*E]
    Q_init[:, 0] = 1.0    # 密度
    Q_init[:, 1] = 0.1    # x动量
    Q_init[:, 2] = 0.0    # y动量  
    Q_init[:, 3] = 2.5    # 总能量
    
    # 求解
    Q_final = solver.solve_steady(Q_init, max_iter=100)
    
    print(f"网格信息: {solver.get_mesh_info()}")
    print(f"最终解的范围: {Q_final.min():.3f} ~ {Q_final.max():.3f}")

def example_unstructured_usage():
    """非结构网格使用示例"""
    print("=== 非结构网格求解示例 ===")
    
    # 模拟非结构网格数据
    n_cells = 100
    n_faces = 200
    
    mesh_data = {
        'cells_node': torch.randint(0, n_faces, (n_cells * 3,)),
        'cells_face': torch.randint(0, n_faces, (n_cells * 3,)),
        'cells_index': torch.repeat_interleave(torch.arange(n_cells), 3),
        'face_node': torch.randint(0, 150, (2, n_faces)),
        'mesh_pos': torch.randn(150, 2),
        'face_area': torch.rand(n_faces, 1),
        'cells_area': torch.rand(n_cells, 1)
    }
    
    solver = create_unstructured_solver(mesh_data)
    
    # 初始化流场
    Q_init = torch.ones(n_cells, 4)
    Q_init[:, 0] = 1.0
    Q_init[:, 1] = 0.1
    Q_init[:, 2] = 0.0
    Q_init[:, 3] = 2.5
    
    # 求解
    Q_final = solver.solve_steady(Q_init, max_iter=50)
    
    print(f"网格信息: {solver.get_mesh_info()}")
    print(f"最终解的范围: {Q_final.min():.3f} ~ {Q_final.max():.3f}")

def compare_methods():
    """比较两种方法的性能"""
    print("=== 方法比较 ===")
    
    import time
    
    # 结构网格
    start_time = time.time()
    solver_struct = create_structured_solver(ni=30, nj=20, dx=0.02, dy=0.02)
    Q_struct = torch.ones(600, 4)
    Q_struct_final = solver_struct.solve_step(Q_struct)
    struct_time = time.time() - start_time
    
    # 非结构网格
    start_time = time.time()
    mesh_data = {
        'cells_node': torch.randint(0, 1000, (1800,)),
        'cells_face': torch.randint(0, 1000, (1800,)),
        'cells_index': torch.repeat_interleave(torch.arange(600), 3),
        'face_node': torch.randint(0, 800, (2, 1000)),
        'mesh_pos': torch.randn(800, 2),
        'face_area': torch.rand(1000, 1),
        'cells_area': torch.rand(600, 1)
    }
    solver_unstruct = create_unstructured_solver(mesh_data)
    Q_unstruct = torch.ones(600, 4)
    Q_unstruct_final = solver_unstruct.solve_step(Q_unstruct)
    unstruct_time = time.time() - start_time
    
    print(f"结构网格求解时间: {struct_time:.4f}s")
    print(f"非结构网格求解时间: {unstruct_time:.4f}s")
    print(f"方法信息:")
    print(f"  结构网格: {solver_struct.get_mesh_info()}")
    print(f"  非结构网格: {solver_unstruct.get_mesh_info()}")

if __name__ == "__main__":
    # 运行示例
    example_structured_usage()
    print()
    example_unstructured_usage()
    print()
    compare_methods()
