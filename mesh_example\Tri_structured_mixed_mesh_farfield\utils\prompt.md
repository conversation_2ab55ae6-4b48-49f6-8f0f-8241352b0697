
你是一名精通计算流体力学(CFD)和Python编程的专家。你的任务是写一个python文件读取一个以CGNS格式存储的2D多块结构化网格，提取坐标、计算邻居关系、识别边界类型，并将最终数据存入一个HDF5文件中。具体来说，你可以参考src/Extract_mesh/parse_comsol.py中的逻辑，为我写一个CGNS解析器。尤其是要调用：
```python
    mesh = {
        "node|pos": torch.from_numpy(self.mesh_file["vertices"]), # 所有域的网格顶点坐标[N,2],N是顶点数量
        "node|surf_mask": torch.from_numpy(surf_mask).bool(), # 表示构成物面的边界点的mask[N,1]，注意这里以边界条件为wall的点可以置为True
        "node|node_type": torch.from_numpy(node_type).long(), # 每个顶点的type类型，我在cgns里面是设置了边界条件的，一共4种或者5种边界条件: inflow,outflow,wall,unspecified,unspecifiedBC，最后两种不确定，反正最后两种你要当成NORMAL对待
        "face|face_node": torch.from_numpy(face_node).long(), # 表示2维下，顶点与顶点索引构成的边数组[2,E]E是边的数量
        "cells_node": torch.from_numpy(cells_node).long(), # [C*C_n，1]这是一个flatten的数组，表示构成单元的顶点索引; C是单元数量,C_n是每个单元的顶点数
        "cells_index": torch.from_numpy(cells_index).long(), # [C*C_n，1]这是一个flatten的数组,与cells_node大小保持一致，通常存储[0,0,0,0,1,1,1,1,2,2,2,2]这样的内容，表示与其对应的cells_node是哪一个单元的索引
        "cells_face": torch.from_numpy(cells_face).long(), # [C*C_f,1]这是一个flatten的数组，表示构成单元的边索引; C是单元数量,C_f是每个单元的边数，注意在2D情况下这个C*C_f=C*C_n，并且cells_face里面存储的内容，实际上就是face|face_node这个数组的下标
    }

    # There`s face_center_pos, centroid, face_type, neighbour_cell, face_node_x need to be resolved
    h5_dataset = extract_mesh_state(
        mesh,
        path=self.path,
    )
```
这个函数，以实现代码复用。注意：传入extract_mesh_state函数的mesh字典里面所有属性应该是整个网格全局的，即：每个属性应该是非结构部分和结构部分的cat起来的结果

1.  **环境准备:**
    * 请确定并使用合适的Python库（如 `pyCGNS`）来读取CGNS文件。
    * 请使用 `pytorch` 来处理数据，并将最终结果存储在 `HDF5` 文件中。

**输入文件:**
2.  `mesh_example/Tri_structured_mixed_mesh_farfield/Tri_structured_mixed_mesh_farfield.cgns`: 一个基于cgns格式的2D网格文件，这是个混合网格，同时包含非结构的三角形域，以及多块的结构网格域，以及给定的边界条件。

**额外任务处理流程:**

由于这是一个非结构/结构混合网格，因此我还需要你帮我把所有结构域部分的网格提取出来，额外生成结构域部分的block info
block info是一个单独的字典，你可以尝试修改extract_mesh_state函数，来把这部分的内容也写入h5文件

2.  **坐标处理 (使用 `Tri_structured_mixed_mesh_farfield.cgns`):**
    * 读取CGNS文件中的所有结构域的块(block)。
    * 将每个块的坐标（i, j 格式）转换为一个形状为 `(i, j, 2)` 的torch数组。命名为`struc_pos`
    * 将每个块的坐标展平(reshape)为 `(i*j, 2)` 的形式。
    * 按顺序将所有块展平后的坐标数组沿第一个维度(axis=0)拼接(concatenate)，生成一个结构域全局坐标数组 `unstruc_pos`，其形状为 `(N, 2)`，其中 `N` 是网格中所有节点的总数。

3.  **邻居索引计算 (使用 `unstruc_pos`):**
    **目的是为了后续实现中心差分格式的有限差分进行节点处的梯度计算**
    * 创建一个整数类型的torch数组 `neighbor_idx`，形状为 `(N, 4)`，用于存储每个节点的上、下、左、右四个方向的邻居索引,可以用卷积实现。
    * **索引规则:** 所有的索引值都必须是该邻居节点在 `unstruc_pos` 数组中的下标。
    * **边界处理:**
        * **物理边界:** 如果一个节点的某个邻居超出了整个计算域的物理边界，则将对应的邻居索引设为 `-1`。
        * **块间交界面(Interface):** 如果一个节点的邻居位于另一个相邻的块中，你必须准确找到该邻居节点的全局索引，并填入 `global_neighbor_idx` 中。**不要**将这种情况下的索引设为-1。（你看看cgns里面是不是已经帮你存好了相关信息）

4.  **利用neighbor_idx去计算计算域域物理域转换的网格导数:**
    * 创建一个整数类型的torch数组 `grid_metric`:[unstruc_pos.shape[0],2,2]其中[2,2]所在的维度表示[[akesai/ax,akesai/ay],[aeta/ax,aeta/ay]]
    * 其中akesai表示ksi方向的导数，aeta表示eta方向的导数，ax表示x方向的导数，ay表示y方向的导数

5. **使用结构域的所有坐标新建一个人造函数测试中心差分梯度重构精度**
    * 人造函数为 sin(x)^2+sin(xy)+sin(y)^2
    * 计算该函数在所有节点上的解析导数
    * 计算该函数在所有节点上的中心差分导数
    * 计算解析导数和中心差分导数的相对误差
    * 把结构域的gradient_test信息(ground Truth, FD的结果)写入vtk文件.你可以单独写一个函数，将结构域的部分按结构网格的规律写入vtk
