import sys
import os

file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)
import numpy as np
import torch
import h5py
import json
from Extract_mesh.parse_to_h5 import extract_mesh_state, NodeType
from Extract_mesh.parse_base import Basemanager
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from Post_process.to_vtk import write_vtp_file
from Utils.utilities import filter_adj
from scipy.signal import convolve2d

# Import CGNS libraries
import CGNS.MAP as cgm
import CGNS.PAT.cgnslib as cgns
import CGNS.PAT.cgnskeywords as cgk
import CGNS.PAT.cgnsutils as cgu


class CGNS_manager(Basemanager):
    def __init__(
        self,
        mesh_file,
        data_file=None,
        file_dir=None,
        case_name=None,
        path=None,
    ):
        self.path = path
        self.case_name = case_name
        self.file_dir = file_dir
        self.mesh_file_path = mesh_file
        
        # Read BC configuration
        with open(f"{file_dir}/BC.json", "r") as f:
            self.bc = json.load(f)
            
        # Process BC ranges
        for bc_type, bc_index_list in self.bc.items(): 
            if bc_index_list is None or not isinstance(bc_index_list, list):
                continue
                
            def process_item(item):
                if isinstance(item, str) and '-' in item:
                    try:
                        start, end = map(int, item.split('-'))
                        return list(range(start, end + 1))
                    except ValueError:
                        raise ValueError(f"Invalid range format in '{item}' for '{bc_type}'")
                elif isinstance(item, list):
                    return [process_item(sub_item) for sub_item in item]
                else:
                    try:
                        return int(item)
                    except ValueError:
                        raise ValueError(f"Invalid item format in '{item}' for '{bc_type}'")

            def flatten_list(nested_list):
                flattened = []
                for item in nested_list:
                    if isinstance(item, list):
                        flattened.extend(flatten_list(item))
                    else:
                        flattened.append(item)
                return flattened

            processed_list = [process_item(item) for item in bc_index_list]
            flattened_list = flatten_list(processed_list)
            self.bc[bc_type] = flattened_list
            
        # Read CGNS file
        self.cgns_data = {"structured": [], "unstructured": []}
        self.read_cgns_file(mesh_file)
        
    def read_cgns_file(self, filename):
        """Read CGNS file and extract mesh data using pyCGNS"""
        print(f"Reading CGNS file: {filename}")

        # Load CGNS tree using pyCGNS
        tree, links, paths = cgm.load(filename)
        vertex_offset = 0

        # Find base node
        base_node = None
        for node in tree[2]:
            if node[3] == 'CGNSBase_t':
                base_node = node
                break

        if base_node is None:
            raise ValueError("No CGNSBase_t node found in CGNS file")

        base_name = base_node[0]
        print(f"Base name: {base_name}")

        # Find all zone nodes
        zone_nodes = []
        for node in base_node[2]:
            if node[3] == 'Zone_t':
                zone_nodes.append(node)

        print(f"Number of zones: {len(zone_nodes)}")

        for zone_idx, zone_node in enumerate(zone_nodes):
            zone_name = zone_node[0]
            zone_size = zone_node[1]  # Zone size array
            print(f"Zone {zone_idx + 1}: {zone_name}")
            print(f"Zone size: {zone_size}")

            # Determine zone type based on zone size dimensions
            # For structured zones: zone_size has shape (2, 3) with [[ni, nj, nk], [ni-1, nj-1, nk-1]]
            # For unstructured zones: zone_size has shape (1, 3) with [[nvertices, ncells, 0]]
            if zone_size.shape[0] == 1:  # Single row means unstructured
                zone_type = 'Unstructured'
            else:  # Multiple rows means structured
                zone_type = 'Structured'
            print(f"Zone type: {zone_type}")

            zone_data = {
                'name': zone_name,
                'size': zone_size,
                'type': zone_type,
                'vertex_offset': vertex_offset,
                'elements': [],
                'zone_node': zone_node  # Store the zone node for boundary condition processing
            }

            if zone_type == 'Structured':
                # Handle structured zone
                zone_data.update(self._read_structured_zone_pycgns(zone_node, zone_size))
                # Test finite difference gradient reconstruction on structured domains
                # if len(zone_data['struc_pos']) > 0:
                #     gradient_test = self.test_finite_difference_gradient(
                #         zone_data['struc_pos'],
                #         zone_data['neighbor_idx'],
                #         zone_data['grid_metrics']
                #     )

                self.cgns_data["structured"].append(zone_data)
                # Update vertex offset for next zone
                vertex_offset += zone_data['unstruct_pos'].shape[0]
            else:
                # Handle unstructured zone
                zone_data.update(self._read_unstructured_zone_pycgns(zone_node, zone_size))
                self.cgns_data["unstructured"].append(zone_data)
                # Update vertex offset for next zone
                vertex_offset += zone_data['vertices'].shape[0]

        # Combine all vertices from all zones into a global array
        all_vertices = []
        for zone_data in self.cgns_data["unstructured"]:
            all_vertices.append(zone_data['vertices'])
        for zone_data in self.cgns_data["structured"]:
            all_vertices.append(zone_data['unstruct_pos'])  #由结构网格展平得到

        if all_vertices:
            self.cgns_data["vertices"] = np.vstack(all_vertices)
        else:
            self.cgns_data["vertices"] = np.array([]).reshape(0, 2)


    def _read_structured_zone_pycgns(self, zone_node, zone_size):
        """Read structured zone data using pyCGNS"""
        zone_data = {}

        # For structured zones, zone_size is [[ni, nj, nk], [ni-1, nj-1, nk-1]]
        if zone_size.shape[0] == 1:
            # If zone_size has only one row, it means it's actually unstructured
            raise ValueError(f"Zone {zone_node[0]} appears to be unstructured, not structured")

        # For structured zones, zone_size is [[ni, nj, nk], [ni-1, nj-1, nk-1]]
        # The first row contains the vertex dimensions, second row contains cell dimensions
        # But sometimes the actual coordinate data might have different dimensions
        ni, nj = zone_size[0, 0], zone_size[0, 1]  # Vertex dimensions
        print(f"Structured zone dimensions from zone_size: {ni} x {nj} vertices")
        print(f"Zone size array: {zone_size}")

        # Find GridCoordinates node
        grid_coords_node = None
        for child in zone_node[2]:
            if child[3] == 'GridCoordinates_t':
                grid_coords_node = child
                break

        if grid_coords_node is None:
            raise ValueError(f"No GridCoordinates_t found in zone {zone_node[0]}")

        # Read coordinate data
        x_coords = None
        y_coords = None

        for coord_node in grid_coords_node[2]:
            if coord_node[3] == 'DataArray_t':
                coord_name = coord_node[0]
                coord_data = coord_node[1]

                if coord_name == 'CoordinateX':
                    x_coords = coord_data
                elif coord_name == 'CoordinateY':
                    y_coords = coord_data

        if x_coords is None or y_coords is None:
            raise ValueError(f"Missing coordinate data in zone {zone_node[0]}")

        # Handle coordinate array dimensions properly
        # Sometimes the actual coordinate data doesn't match the zone_size exactly
        print(f"Original coordinate shape: {x_coords.shape}")
        print(f"Expected zone dimensions from zone_size: ni={ni}, nj={nj}")

        coord_shape = x_coords.shape

        # Use the actual coordinate shape to determine grid dimensions
        if len(coord_shape) == 2:
            actual_ni, actual_nj = coord_shape
            print(f"Using actual coordinate dimensions: ni={actual_ni}, nj={actual_nj}")

            # Update ni, nj to match actual coordinate data
            ni, nj = actual_ni, actual_nj
            x_grid = x_coords
            y_grid = y_coords

        elif x_coords.size == ni * nj:
            # Coordinates are flattened - reshape to expected dimensions
            x_grid = x_coords.reshape((ni, nj))
            y_grid = y_coords.reshape((ni, nj))
            print(f"Reshaping flattened coordinates to ({ni}, {nj}) format")
        else:
            raise ValueError(f"Cannot handle coordinate array shape: {coord_shape}")

        # Store structured grid information
        zone_data['x_grid'] = x_grid
        zone_data['y_grid'] = y_grid
        zone_data['ni'] = ni
        zone_data['nj'] = nj

        # Flatten coordinates for global vertex array using proper ordering
        # Use C-style flattening to maintain (i,j) -> linear index mapping
        # C-style flattening: for j in range(nj): for i in range(ni): idx = j*ni + i
        struct_pos = np.stack([x_grid, y_grid], axis=-1)
        unstruct_pos = np.stack([x_grid.flatten(order='C'), y_grid.flatten(order='C')], axis=1)
        zone_data['struct_pos'] = struct_pos
        zone_data['unstruct_pos'] = unstruct_pos

        # Generate structured elements (quads) using convolution approach
        # Create 2D node index array with shape (ni, nj) to match C-style flattening
        struct_node_idx = np.arange(unstruct_pos.shape[0]).reshape((ni, nj), order='C')
        zone_data['struct_node_idx'] = struct_node_idx

        # Define convolution kernels to extract the 4 corners of each quad element
        # For a 2x2 kernel on a structured grid, we want to extract:
        # [v0, v1]  ->  v0: bottom-left, v1: bottom-right
        # [v3, v2]  ->  v3: top-left,    v2: top-right
        # Counter-clockwise ordering: [v0, v1, v2, v3]

        kernel_v0 = np.array([[1, 0],   # bottom-left corner
                              [0, 0]], dtype=int)
        kernel_v1 = np.array([[0, 1],   # bottom-right corner
                              [0, 0]], dtype=int)
        kernel_v2 = np.array([[0, 0],   # top-right corner
                              [0, 1]], dtype=int)
        kernel_v3 = np.array([[0, 0],   # top-left corner
                              [1, 0]], dtype=int)

        # Apply convolution to get node indices for each corner of all elements
        elements_v0 = convolve2d(struct_node_idx, kernel_v0, mode='valid')
        elements_v1 = convolve2d(struct_node_idx, kernel_v1, mode='valid')
        elements_v2 = convolve2d(struct_node_idx, kernel_v2, mode='valid')
        elements_v3 = convolve2d(struct_node_idx, kernel_v3, mode='valid')

        # Stack the corners to form quad elements [v0, v1, v2, v3] (counter-clockwise)
        elements = np.stack((elements_v0, elements_v1, elements_v2, elements_v3), axis=-1)

        # Reshape to get final element connectivity array
        elements = elements.reshape(-1, 4)

        zone_data['elements'] = elements.astype(np.int64)
        zone_data['element_type'] = 'quad'

        # 计算 neighbor idx信息 (使用局部索引，稍后在全局处理时会加上偏移)
        neighbor_idx = self._compute_structured_neighbors(ni, nj, global_offset=0)
        zone_data['neighbor_idx'] = neighbor_idx

        # 计算网格metrics 信息
        grid_metrics = self._compute_grid_metrics(unstruct_pos, neighbor_idx)
        zone_data['grid_metrics'] = grid_metrics
        
        return zone_data

    def _read_unstructured_zone_pycgns(self, zone_node, zone_size):
        """Read unstructured zone data using pyCGNS"""
        zone_data = {}

        # For unstructured zones, zone_size is [[nvertices, ncells, 0]]
        nvertices = zone_size[0, 0]
        ncells = zone_size[0, 1]
        print(f"Unstructured zone: {nvertices} vertices, {ncells} cells")

        # Find GridCoordinates node
        grid_coords_node = None
        for child in zone_node[2]:
            if child[3] == 'GridCoordinates_t':
                grid_coords_node = child
                break

        if grid_coords_node is None:
            raise ValueError(f"No GridCoordinates_t found in zone {zone_node[0]}")

        # Read coordinate data
        x_coords = None
        y_coords = None

        for coord_node in grid_coords_node[2]:
            if coord_node[3] == 'DataArray_t':
                coord_name = coord_node[0]
                coord_data = coord_node[1]

                if coord_name == 'CoordinateX':
                    x_coords = coord_data
                elif coord_name == 'CoordinateY':
                    y_coords = coord_data

        if x_coords is None or y_coords is None:
            raise ValueError(f"Missing coordinate data in zone {zone_node[0]}")

        vertices = np.stack([x_coords, y_coords], axis=1)
        zone_data['vertices'] = vertices

        # Read element connectivity
        elements = []
        element_types = []

        # Find element sections
        for child in zone_node[2]:
            if child[3] == 'Elements_t':
                section_name = child[0]
                print(f"Element section: {section_name}")

                # Find ElementConnectivity node
                connectivity_node = None
                for elem_child in child[2]:
                    if elem_child[0] == 'ElementConnectivity' and elem_child[3] == 'DataArray_t':
                        connectivity_node = elem_child
                        break

                if connectivity_node is not None:
                    connectivity_data = connectivity_node[1]

                    # Determine element type based on section name
                    if 'Tri' in section_name:
                        # Triangle elements
                        connectivity = connectivity_data.reshape(-1, 3) - 1  # Convert to 0-based indexing
                        elements.append(connectivity)
                        element_types.extend(['tri'] * connectivity.shape[0])
                    elif 'Quad' in section_name:
                        # Quad elements
                        connectivity = connectivity_data.reshape(-1, 4) - 1  # Convert to 0-based indexing
                        elements.append(connectivity)
                        element_types.extend(['quad'] * connectivity.shape[0])
                    elif 'Bar' in section_name:
                        # Boundary elements (edges)
                        connectivity = connectivity_data.reshape(-1, 2) - 1  # Convert to 0-based indexing
                        zone_data['boundary_edges'] = connectivity

        if elements:
            zone_data['elements'] = np.vstack(elements)
            zone_data['element_types'] = element_types

        return zone_data

    def set_node_type(self):
        """Set node types based on boundary conditions from CGNS file"""
        pos = self.cgns_data["vertices"]
        node_type = np.full((pos.shape[0]), NodeType.NORMAL)
        surf_mask = np.full((pos.shape[0]), False)

        # Read boundary conditions from CGNS file
        boundary_nodes = self._extract_boundary_conditions()

        # Apply boundary conditions
        for bc_type, node_indices in boundary_nodes.items():
            if len(node_indices) > 0:
                if bc_type == "inflow":
                    node_type[node_indices] = NodeType.INFLOW
                elif bc_type == "outflow" or bc_type == "outlet":
                    node_type[node_indices] = NodeType.OUTFLOW
                elif bc_type == "wall":
                    node_type[node_indices] = NodeType.WALL_BOUNDARY
                    surf_mask[node_indices] = True
                elif bc_type in ["unspecified", "unspecifiedBC"]:
                    node_type[node_indices] = NodeType.NORMAL
                else:
                    print(f"Unknown boundary condition: {bc_type}, treating as NORMAL")
                    node_type[node_indices] = NodeType.NORMAL

        return node_type, surf_mask, None, np.full_like(node_type, -1, dtype=np.float64)

    def _extract_boundary_conditions(self):
        """Extract boundary condition information from CGNS file using pyCGNS"""
        boundary_nodes = {
            "inflow": [],
            "outflow": [],
            "outlet": [],
            "wall": [],
            "unspecified": [],
            "unspecifiedBC": []
        }

        # BC type mapping
        bc_type_map = {
            'inlet': 'inflow',
            'outlet': 'outflow',
            'wall': 'wall',
            'farfield': 'outflow',
            'symmetry': 'wall'
        }

        # Process each zone to find boundary conditions
        all_zones = self.cgns_data["unstructured"] + self.cgns_data["structured"]
        for zone_data in all_zones:
            zone_name = zone_data['name']
            zone_node = zone_data['zone_node']
            zone_type = zone_data['type']

            
            print(f"Processing boundary conditions for zone: {zone_name} (type: {zone_type})")

            # Find ZoneBC node
            zone_bc_node = self._find_child_by_type(zone_node, 'ZoneBC_t')

            if zone_bc_node:
                self._process_zone_bc(zone_bc_node, zone_data, bc_type_map, boundary_nodes)

            # Process direct boundary element sections
            self._process_element_sections(zone_node, zone_data, zone_bc_node, bc_type_map, boundary_nodes)

        # Convert to numpy arrays and remove duplicates
        for bc_type in boundary_nodes:
            if boundary_nodes[bc_type]:
                boundary_nodes[bc_type] = np.unique(boundary_nodes[bc_type])
                print(f"Total {len(boundary_nodes[bc_type])} {bc_type} boundary nodes")
            else:
                boundary_nodes[bc_type] = np.array([], dtype=int)

        return boundary_nodes

    def _find_child_by_type(self, parent_node, node_type):
        """Find child node by type"""
        for child in parent_node[2]:
            if child[3] == node_type:
                return child
        return None

    def _get_family_name(self, bc_node):
        """Extract FamilyName from BC node"""
        family_name_node = self._find_child_by_type(bc_node, 'FamilyName_t')
        if family_name_node:
            family_name_data = family_name_node[1]
            if hasattr(family_name_data, 'tobytes'):
                return family_name_data.tobytes().decode('ascii', errors='ignore').strip('\x00')
            else:
                return str(family_name_data).strip()
        return None

    def _extract_connectivity_nodes(self, connectivity, vertex_offset):
        """Extract unique node indices from connectivity data"""
        if len(connectivity) > 0:
            bc_node_indices = np.unique(connectivity.flatten()) - 1  # Convert to 0-based
            global_bc_indices = bc_node_indices + vertex_offset
            # Filter valid indices
            return global_bc_indices[global_bc_indices < len(self.cgns_data["vertices"])]
        return np.array([], dtype=int)

    def _process_zone_bc(self, zone_bc_node, zone_data, bc_type_map, boundary_nodes):
        """Process ZoneBC node for boundary conditions"""
        zone_type = zone_data['type']

        for bc_child in zone_bc_node[2]:
            if bc_child[3] == 'BC_t':
                bc_name = bc_child[0]
                print(f"  Found BC: {bc_name}")

                family_name = self._get_family_name(bc_child)
                bc_type = bc_type_map.get(family_name, 'unspecified')
                print(f"    FamilyName: {family_name} -> BC type: {bc_type}")

                if zone_type == 'Structured':
                    self._process_structured_bc(bc_child, zone_data, bc_type, boundary_nodes)
                else:
                    self._process_unstructured_bc(bc_child, zone_data, bc_type, boundary_nodes)

    def _process_structured_bc(self, bc_child, zone_data, bc_type, boundary_nodes):
        """Process structured zone boundary condition"""
        point_range_node = self._find_child_by_type(bc_child, 'IndexRange_t')
        if point_range_node:
            point_range = point_range_node[1]
            struct_node_idx = zone_data['struct_node_idx']

            bc_node_indices = self._extract_structured_boundary_nodes(
                point_range, struct_node_idx
            ) + zone_data['vertex_offset']

            if len(bc_node_indices) > 0:
                boundary_nodes[bc_type].extend(bc_node_indices.tolist())
                print(f"    Added {len(bc_node_indices)} nodes for {bc_type} (structured)")

    def _process_unstructured_bc(self, bc_child, zone_data, bc_type, boundary_nodes):
        """Process unstructured zone boundary condition"""
        bc_name = bc_child[0]
        zone_node = zone_data['zone_node']

        # Find corresponding element section
        for elem_child in zone_node[2]:
            if elem_child[3] == 'Elements_t' and elem_child[0] == bc_name:
                connectivity_node = self._find_child_by_type(elem_child, 'ElementConnectivity_t')
                if connectivity_node:
                    connectivity = connectivity_node[1]
                    valid_indices = self._extract_connectivity_nodes(connectivity, zone_data['vertex_offset'])
                    boundary_nodes[bc_type].extend(valid_indices.tolist())
                    print(f"    Added {len(valid_indices)} nodes for {bc_type} (unstructured)")
                break

    def _process_element_sections(self, zone_node, zone_data, zone_bc_node, bc_type_map, boundary_nodes):
        """Process element sections for boundary conditions"""
        zone_type = zone_data['type']

        for child in zone_node[2]:
            if child[3] == 'Elements_t':
                section_name = child[0]
                print(f"  Checking element section: {section_name}")

                # Check direct boundary section mapping
                if section_name in bc_type_map:
                    bc_type = bc_type_map[section_name]
                    print(f"    Found boundary section: {section_name} -> {bc_type}")
                    self._add_section_nodes(child, zone_data, bc_type, boundary_nodes, "direct section")

                # For unstructured zones, check ZoneBC mapping
                elif zone_type == 'Unstructured' and zone_bc_node:
                    bc_type = self._find_bc_type_from_zone_bc(zone_bc_node, section_name, bc_type_map)
                    if bc_type:
                        self._add_section_nodes(child, zone_data, bc_type, boundary_nodes, f"unstructured BC section: {section_name}")
                else:
                    print(f"    Section {section_name} is not a boundary section")

    def _find_bc_type_from_zone_bc(self, zone_bc_node, section_name, bc_type_map):
        """Find BC type from ZoneBC node for given section name"""
        for bc_child in zone_bc_node[2]:
            if bc_child[3] == 'BC_t' and bc_child[0] == section_name:
                family_name = self._get_family_name(bc_child)
                if family_name:
                    return bc_type_map.get(family_name, 'unspecified')
        return None

    def _add_section_nodes(self, elem_section, zone_data, bc_type, boundary_nodes, description):
        """Add nodes from element section to boundary_nodes"""
        connectivity_node = None
        for elem_child in elem_section[2]:
            if elem_child[0] == 'ElementConnectivity' and elem_child[3] == 'DataArray_t':
                connectivity_node = elem_child
                break

        if connectivity_node:
            connectivity = connectivity_node[1]
            valid_indices = self._extract_connectivity_nodes(connectivity, zone_data['vertex_offset'])
            boundary_nodes[bc_type].extend(valid_indices.tolist())
            print(f"    Added {len(valid_indices)} nodes for {bc_type} ({description})")
        else:
            print(f"    No ElementConnectivity found for {elem_section[0]}")

    def _extract_structured_boundary_nodes(self, point_range, struct_node_idx):
        """Extract boundary node indices from structured zone point range"""
        # point_range is [[i1, j1], [i2, j2]] in 1-based indexing
        i1, j1 = point_range[0] - 1  # Convert to 0-based
        i2, j2 = point_range[1] - 1  # Convert to 0-based

        # Handle different boundary orientations
        boundary_indices = struct_node_idx[i1:(i2+1), j1:(j2+1)].flatten(order='C')

        return boundary_indices

    def _compute_structured_neighbors(self, ni, nj, global_offset):
        """Compute neighbor indices for structured grid using convolution approach"""
        # Create 2D node index array with shape (ni, nj) to match C-style flattening
        # C-style flattening: for j in range(nj): for i in range(ni): idx = j*ni + i
        zone_node_idx = np.arange(ni * nj).reshape((ni, nj), order='C') + global_offset

        # Pad with -1 around the boundary to handle edge cases
        padded_idx = np.pad(zone_node_idx, ((1, 1), (1, 1)), mode='constant', constant_values=-1)

        # Define 3x3 kernels to extract neighbors
        # Center point is at (1,1), neighbors are at adjacent positions
        kernel_left = np.array([[0, 0, 0],    # Left neighbor (i-1, j)
                                [1, 0, 0],
                                [0, 0, 0]], dtype=int)

        kernel_right = np.array([[0, 0, 0],   # Right neighbor (i+1, j)
                                 [0, 0, 1],
                                 [0, 0, 0]], dtype=int)

        kernel_bottom = np.array([[0, 0, 0],  # Bottom neighbor (i, j-1)
                                  [0, 0, 0],
                                  [0, 1, 0]], dtype=int)

        kernel_top = np.array([[0, 1, 0],     # Top neighbor (i, j+1)
                               [0, 0, 0],
                               [0, 0, 0]], dtype=int)

        # Apply convolution to get neighbor indices
        left_neighbors = convolve2d(padded_idx, kernel_left, mode='valid')
        right_neighbors = convolve2d(padded_idx, kernel_right, mode='valid')
        bottom_neighbors = convolve2d(padded_idx, kernel_bottom, mode='valid')
        top_neighbors = convolve2d(padded_idx, kernel_top, mode='valid')

        # Stack neighbors: [left, right, bottom, top] for each node
        neighbor_idx = np.stack([
            left_neighbors.flatten(order='C'),
            right_neighbors.flatten(order='C'),
            bottom_neighbors.flatten(order='C'),
            top_neighbors.flatten(order='C')
        ], axis=1)

        return neighbor_idx

    def _compute_grid_metrics(self, unstruc_pos, neighbor_idx):
        """Compute grid transformation metrics using neighbor indices"""
        n_points = unstruc_pos.shape[0]
        metrics = np.zeros((n_points, 2, 2))

        # neighbor_idx:[N,4], [left, right, bottom, top] neighbor node indices
        # Extract neighbor indices
        left_neighbors = neighbor_idx[:, 0]    # i-1
        right_neighbors = neighbor_idx[:, 1]   # i+1
        bottom_neighbors = neighbor_idx[:, 2]  # j-1
        top_neighbors = neighbor_idx[:, 3]     # j+1

        # Initialize derivatives
        dx_dxi = np.zeros(n_points)
        dy_dxi = np.zeros(n_points)
        dx_deta = np.zeros(n_points)
        dy_deta = np.zeros(n_points)

        # Compute xi direction derivatives (dx/dxi, dy/dxi) using left-right neighbors
        # Central difference where both neighbors exist
        both_xi_mask = (left_neighbors != -1) & (right_neighbors != -1)
        if np.any(both_xi_mask):
            dx_dxi[both_xi_mask] = 0.5 * (
                unstruc_pos[right_neighbors[both_xi_mask], 0] -
                unstruc_pos[left_neighbors[both_xi_mask], 0]
            )
            dy_dxi[both_xi_mask] = 0.5 * (
                unstruc_pos[right_neighbors[both_xi_mask], 1] -
                unstruc_pos[left_neighbors[both_xi_mask], 1]
            )

        # Forward difference where only right neighbor exists
        only_right_mask = (left_neighbors == -1) & (right_neighbors != -1)
        if np.any(only_right_mask):
            indices = np.where(only_right_mask)[0]
            dx_dxi[only_right_mask] = (
                unstruc_pos[right_neighbors[only_right_mask], 0] -
                unstruc_pos[indices, 0]
            )
            dy_dxi[only_right_mask] = (
                unstruc_pos[right_neighbors[only_right_mask], 1] -
                unstruc_pos[indices, 1]
            )

        # Backward difference where only left neighbor exists
        only_left_mask = (left_neighbors != -1) & (right_neighbors == -1)
        if np.any(only_left_mask):
            indices = np.where(only_left_mask)[0]
            dx_dxi[only_left_mask] = (
                unstruc_pos[indices, 0] -
                unstruc_pos[left_neighbors[only_left_mask], 0]
            )
            dy_dxi[only_left_mask] = (
                unstruc_pos[indices, 1] -
                unstruc_pos[left_neighbors[only_left_mask], 1]
            )

        # Compute eta direction derivatives (dx/deta, dy/deta) using bottom-top neighbors
        # Central difference where both neighbors exist
        both_eta_mask = (bottom_neighbors != -1) & (top_neighbors != -1)
        if np.any(both_eta_mask):
            dx_deta[both_eta_mask] = 0.5 * (
                unstruc_pos[top_neighbors[both_eta_mask], 0] -
                unstruc_pos[bottom_neighbors[both_eta_mask], 0]
            )
            dy_deta[both_eta_mask] = 0.5 * (
                unstruc_pos[top_neighbors[both_eta_mask], 1] -
                unstruc_pos[bottom_neighbors[both_eta_mask], 1]
            )

        # Forward difference where only top neighbor exists
        only_top_mask = (bottom_neighbors == -1) & (top_neighbors != -1)
        if np.any(only_top_mask):
            indices = np.where(only_top_mask)[0]
            dx_deta[only_top_mask] = (
                unstruc_pos[top_neighbors[only_top_mask], 0] -
                unstruc_pos[indices, 0]
            )
            dy_deta[only_top_mask] = (
                unstruc_pos[top_neighbors[only_top_mask], 1] -
                unstruc_pos[indices, 1]
            )

        # Backward difference where only bottom neighbor exists
        only_bottom_mask = (bottom_neighbors != -1) & (top_neighbors == -1)
        if np.any(only_bottom_mask):
            indices = np.where(only_bottom_mask)[0]
            dx_deta[only_bottom_mask] = (
                unstruc_pos[indices, 0] -
                unstruc_pos[bottom_neighbors[only_bottom_mask], 0]
            )
            dy_deta[only_bottom_mask] = (
                unstruc_pos[indices, 1] -
                unstruc_pos[bottom_neighbors[only_bottom_mask], 1]
            )

        # Compute Jacobian
        J = dx_dxi * dy_deta - dx_deta * dy_dxi

        # Handle small Jacobians
        small_jacobian_mask = np.abs(J) < 1e-12
        if np.any(small_jacobian_mask):
            print(f"Warning: {np.sum(small_jacobian_mask)} points with small Jacobian")
            J[small_jacobian_mask] = np.sign(J[small_jacobian_mask]) * 1e-12

        # Compute inverse transformation metrics
        # [dxi/dx, dxi/dy]
        # [deta/dx, deta/dy]
        metrics[:, 0, 0] = dy_deta / J   # dxi/dx
        metrics[:, 0, 1] = -dx_deta / J  # dxi/dy
        metrics[:, 1, 0] = -dy_dxi / J   # deta/dx
        metrics[:, 1, 1] = dx_dxi / J    # deta/dy

        return metrics

    def test_finite_difference_gradient(self, struc_global_pos, struc_neighbor_idx, grid_metrics):
        """Test finite difference gradient reconstruction accuracy using vectorized operations"""
        print("Testing finite difference gradient reconstruction...")

        # Print grid statistics for debugging
        x = struc_global_pos[:, 0]
        y = struc_global_pos[:, 1]
        print(f"Grid x range: [{np.min(x):.6f}, {np.max(x):.6f}]")
        print(f"Grid y range: [{np.min(y):.6f}, {np.max(y):.6f}]")

        # Use a simpler test function for better accuracy assessment
        # Linear function: f = 2*x + 3*y (should have zero error for exact derivatives)
        func_values = 2.0 * x + 3.0 * y

        # Analytical derivatives (constant for linear function)
        dfx_analytical = np.full_like(x, 2.0)
        dfy_analytical = np.full_like(y, 3.0)

        # Vectorized finite difference computation
        n_points = len(struc_global_pos)
        dxi_func = np.zeros(n_points)
        deta_func = np.zeros(n_points)

        # Extract neighbor indices
        left_neighbors = struc_neighbor_idx[:, 0]    # i-1
        right_neighbors = struc_neighbor_idx[:, 1]   # i+1
        bottom_neighbors = struc_neighbor_idx[:, 2]  # j-1
        top_neighbors = struc_neighbor_idx[:, 3]     # j+1

        # Vectorized xi direction differences
        # Central difference where both neighbors exist
        both_xi_mask = (left_neighbors != -1) & (right_neighbors != -1)
        if np.any(both_xi_mask):
            dxi_func[both_xi_mask] = 0.5 * (
                func_values[right_neighbors[both_xi_mask]] -
                func_values[left_neighbors[both_xi_mask]]
            )

        # Forward difference where only right neighbor exists
        only_right_mask = (left_neighbors == -1) & (right_neighbors != -1)
        if np.any(only_right_mask):
            indices = np.where(only_right_mask)[0]
            dxi_func[only_right_mask] = (
                func_values[right_neighbors[only_right_mask]] -
                func_values[indices]
            )

        # Backward difference where only left neighbor exists
        only_left_mask = (left_neighbors != -1) & (right_neighbors == -1)
        if np.any(only_left_mask):
            indices = np.where(only_left_mask)[0]
            dxi_func[only_left_mask] = (
                func_values[indices] -
                func_values[left_neighbors[only_left_mask]]
            )

        # Vectorized eta direction differences
        # Central difference where both neighbors exist
        both_eta_mask = (bottom_neighbors != -1) & (top_neighbors != -1)
        if np.any(both_eta_mask):
            deta_func[both_eta_mask] = 0.5 * (
                func_values[top_neighbors[both_eta_mask]] -
                func_values[bottom_neighbors[both_eta_mask]]
            )

        # Forward difference where only top neighbor exists
        only_top_mask = (bottom_neighbors == -1) & (top_neighbors != -1)
        if np.any(only_top_mask):
            indices = np.where(only_top_mask)[0]
            deta_func[only_top_mask] = (
                func_values[top_neighbors[only_top_mask]] -
                func_values[indices]
            )

        # Backward difference where only bottom neighbor exists
        only_bottom_mask = (bottom_neighbors != -1) & (top_neighbors == -1)
        if np.any(only_bottom_mask):
            indices = np.where(only_bottom_mask)[0]
            deta_func[only_bottom_mask] = (
                func_values[indices] -
                func_values[bottom_neighbors[only_bottom_mask]]
            )

        # Transform to physical coordinates using grid metrics (vectorized)
        dfx_fd = grid_metrics[:, 0, 0] * dxi_func + grid_metrics[:, 1, 0] * deta_func
        dfy_fd = grid_metrics[:, 0, 1] * dxi_func + grid_metrics[:, 1, 1] * deta_func

        # Compute relative errors
        dfx_error = np.abs(dfx_fd - dfx_analytical) / (np.abs(dfx_analytical) + 1e-12)
        dfy_error = np.abs(dfy_fd - dfy_analytical) / (np.abs(dfy_analytical) + 1e-12)

        print(f"Max relative error in df/dx: {np.max(dfx_error):.6e}")
        print(f"Mean relative error in df/dx: {np.mean(dfx_error):.6e}")
        print(f"Max relative error in df/dy: {np.max(dfy_error):.6e}")
        print(f"Mean relative error in df/dy: {np.mean(dfy_error):.6e}")

        return {
            'func_values': func_values,
            'dfx_analytical': dfx_analytical,
            'dfy_analytical': dfy_analytical,
            'dfx_fd': dfx_fd,
            'dfy_fd': dfy_fd,
            'dfx_error': dfx_error,
            'dfy_error': dfy_error
        }

    def element_to_faces(self, elements):
        
        # # Ensure counter-clockwise ordering
        # element_coords = vertices[element_vertices, :]
        # centroid = np.mean(element_coords, axis=0)
        # vectors = element_coords - centroid
        # angles = np.arctan2(vectors[:, 1], vectors[:, 0])
        # sorted_indices = np.argsort(angles)
        # # Reorder element_vertices
        # element_vertices = list(np.array(element_vertices)[sorted_indices])
        
        N_cells, N_cell_nodes = elements.shape
        edge_unordered = []
        for N_node in range(N_cell_nodes-1):
            edge_unordered.append(
                np.stack((elements[:,N_node],elements[:,N_node+1]),axis=1)
            )
        # add last loop
        edge_unordered.append(
            np.stack((elements[:,-1],elements[:,0]),axis=1)
        )
        edge_unordered = np.stack(edge_unordered,axis=1).reshape(-1,2)
        
        edge_sorted = np.sort(edge_unordered,axis=1).T

        # unique_edge,cells_face = np.unique(edge_sorted,axis=1,return_inverse=True)
        
        return edge_sorted

    def extract_mesh(self, mesh_only=True):
        """Extract mesh data and create required data structures"""

        # Process elements from all zones separately to maintain proper indexing
        all_cells_node = []
        all_cells_index = []
        all_edge_lists = []
        count_cells = 0

        # Process each zone separately to maintain proper element-to-zone mapping
        all_zones = self.cgns_data["unstructured"] + self.cgns_data["structured"]
        for zone_data in all_zones:
            if 'elements' in zone_data and len(zone_data['elements']) > 0:
                elements = zone_data['elements']
                vertex_offset = zone_data['vertex_offset']

                # Adjust element indices to global numbering
                global_elements = elements + vertex_offset

                # Add to cells_node
                all_cells_node.append(global_elements.reshape(-1))

                # Create cells_index for this zone
                num_elements = global_elements.shape[0]
                nodes_per_element = global_elements.shape[1]
                cell_index = (
                    (np.arange(num_elements)+count_cells)[:, None]
                    .repeat(axis=1, repeats=nodes_per_element)
                    .reshape(-1, 1)
                )
                all_cells_index.append(cell_index)
                count_cells += num_elements

                # Generate edges for this zone
                edge_sorted = self.element_to_faces(global_elements)
                all_edge_lists.append(edge_sorted)

        # Combine all cells data
        cells_node = np.concatenate(all_cells_node).squeeze()
        cells_index = np.concatenate(all_cells_index).squeeze()

        # Combine all edges and get unique faces
        edge_index_full = np.concatenate(all_edge_lists, axis=1).squeeze()
        face_node, cells_face = np.unique(edge_index_full, axis=1, return_inverse=True)

        # Set node types based on boundary conditions (after all vertices are combined)
        node_type, surf_mask, periodic_idx, periodic_domain = self.set_node_type()

        # Create mesh dictionary with GLOBAL data (all zones combined)
        mesh = {
            "node|pos": torch.from_numpy(self.cgns_data["vertices"]),  # All vertices from all zones
            "node|surf_mask": torch.from_numpy(surf_mask).bool(),
            "node|node_type": torch.from_numpy(node_type).long(),
            "face|face_node": torch.from_numpy(face_node).long(),
            "cells_node": torch.from_numpy(cells_node).long(),
            "cells_index": torch.from_numpy(cells_index).long(),
            "cells_face": torch.from_numpy(cells_face).long(),
        }

        # Call extract_mesh_state to compute additional mesh properties
        h5_dataset = extract_mesh_state(mesh, path=self.path)

        # Save visualization
        self.save_to_vtu(
            mesh=h5_dataset,
            payload={"node|node_type": node_type[:, None]},
            file_name=f"{self.file_dir}/node_type_with_mesh.vtu",
        )

        return h5_dataset

    def write_structured_domain_vtk(self, structured_info, gradient_test):
        """Write structured domain data to VTK files with gradient test results"""
        try:
            import vtk
            from vtk.util import numpy_support
        except ImportError:
            print("Warning: VTK not available, skipping structured domain VTK output")
            return

        structured_zones = structured_info['structured_zones']

        for zone_name, zone_info in structured_zones.items():
            ni, nj = zone_info['ni'], zone_info['nj']
            x_grid = zone_info['x_grid']
            y_grid = zone_info['y_grid']
            global_offset = zone_info['global_offset']

            # Create structured grid
            grid = vtk.vtkStructuredGrid()
            grid.SetDimensions(ni, nj, 1)

            # Create points with correct indexing to match C-style flattening
            # VTK structured grid expects points in (i,j,k) order
            # Our C-style flattening uses: for j in range(nj): for i in range(ni): idx = j*ni + i
            points = vtk.vtkPoints()
            for j in range(nj):
                for i in range(ni):
                    x = x_grid[i, j]  # x_grid is indexed as [i, j]
                    y = y_grid[i, j]  # y_grid is indexed as [i, j]
                    points.InsertNextPoint(x, y, 0.0)
            grid.SetPoints(points)

            # Add gradient test data
            num_points = ni * nj

            # Extract gradient data for this zone and reorder to match VTK point ordering
            zone_data_start = global_offset
            zone_data_end = global_offset + num_points

            # Get data in C-style flattened order (our storage format)
            func_values_flat = gradient_test['func_values'][zone_data_start:zone_data_end]
            dfx_analytical_flat = gradient_test['dfx_analytical'][zone_data_start:zone_data_end]
            dfy_analytical_flat = gradient_test['dfy_analytical'][zone_data_start:zone_data_end]
            dfx_fd_flat = gradient_test['dfx_fd'][zone_data_start:zone_data_end]
            dfy_fd_flat = gradient_test['dfy_fd'][zone_data_start:zone_data_end]
            dfx_error_flat = gradient_test['dfx_error'][zone_data_start:zone_data_end]
            dfy_error_flat = gradient_test['dfy_error'][zone_data_start:zone_data_end]

            # Reshape to 2D grid format and then flatten in VTK order (j-major, i-minor)
            # Our C-style flattening: idx = j*ni + i
            # VTK expects the same order since we're using the same loop structure
            func_values_2d = func_values_flat.reshape((ni, nj), order='C')
            dfx_analytical_2d = dfx_analytical_flat.reshape((ni, nj), order='C')
            dfy_analytical_2d = dfy_analytical_flat.reshape((ni, nj), order='C')
            dfx_fd_2d = dfx_fd_flat.reshape((ni, nj), order='C')
            dfy_fd_2d = dfy_fd_flat.reshape((ni, nj), order='C')
            dfx_error_2d = dfx_error_flat.reshape((ni, nj), order='C')
            dfy_error_2d = dfy_error_flat.reshape((ni, nj), order='C')

            # Flatten in VTK order (same as our C-style order)
            func_values_vtk = func_values_2d.flatten(order='C')
            dfx_analytical_vtk = dfx_analytical_2d.flatten(order='C')
            dfy_analytical_vtk = dfy_analytical_2d.flatten(order='C')
            dfx_fd_vtk = dfx_fd_2d.flatten(order='C')
            dfy_fd_vtk = dfy_fd_2d.flatten(order='C')
            dfx_error_vtk = dfx_error_2d.flatten(order='C')
            dfy_error_vtk = dfy_error_2d.flatten(order='C')

            # Function values
            func_array = numpy_support.numpy_to_vtk(func_values_vtk)
            func_array.SetName("function_values")
            grid.GetPointData().AddArray(func_array)

            # Analytical derivatives
            dfx_analytical_array = numpy_support.numpy_to_vtk(dfx_analytical_vtk)
            dfx_analytical_array.SetName("dfx_analytical")
            grid.GetPointData().AddArray(dfx_analytical_array)

            dfy_analytical_array = numpy_support.numpy_to_vtk(dfy_analytical_vtk)
            dfy_analytical_array.SetName("dfy_analytical")
            grid.GetPointData().AddArray(dfy_analytical_array)

            # Finite difference derivatives
            dfx_fd_array = numpy_support.numpy_to_vtk(dfx_fd_vtk)
            dfx_fd_array.SetName("dfx_finite_difference")
            grid.GetPointData().AddArray(dfx_fd_array)

            dfy_fd_array = numpy_support.numpy_to_vtk(dfy_fd_vtk)
            dfy_fd_array.SetName("dfy_finite_difference")
            grid.GetPointData().AddArray(dfy_fd_array)

            # Errors
            dfx_error_array = numpy_support.numpy_to_vtk(dfx_error_vtk)
            dfx_error_array.SetName("dfx_relative_error")
            grid.GetPointData().AddArray(dfx_error_array)

            dfy_error_array = numpy_support.numpy_to_vtk(dfy_error_vtk)
            dfy_error_array.SetName("dfy_relative_error")
            grid.GetPointData().AddArray(dfy_error_array)

            # Write to file
            writer = vtk.vtkStructuredGridWriter()
            filename = f"{self.file_dir}/{zone_name}_gradient_test.vtk"
            writer.SetFileName(filename)
            writer.SetInputData(grid)
            writer.Write()

            print(f"Structured domain VTK file written: {filename}")


# Define the processing function
def process_file(plot, file_path, path, queue):
    file_name = os.path.basename(file_path)
    file_dir = os.path.dirname(file_path)
    case_name = os.path.basename(file_dir)
    path["file_dir"] = file_dir
    path["case_name"] = case_name
    path["file_name"] = file_name

    # Start convert function
    if file_path.endswith(".cgns"):
        data = CGNS_manager(
            mesh_file=file_path,
            data_file=None,
            file_dir=file_dir,
            case_name=case_name,
            path=path,
        )
    else:
        return None

    h5_dataset = data.extract_mesh(mesh_only=path["mesh_only"])

    # Put the results in the queue
    queue.put((h5_dataset, case_name, file_dir))


# Writer process function
def writer_process(queue, path):
    while True:
        # Get data from queue
        h5_data, case_name, file_dir = queue.get()

        # Break if None is received (sentinel value)
        if h5_data is None:
            break

        os.makedirs(file_dir, exist_ok=True)
        h5_writer = h5py.File(f"{file_dir}/{case_name}.h5", "w")

        # Write dataset key value
        group = h5_writer.create_group(case_name)
        for key, value in h5_data.items():
            if key in group:
                del group[key]

            # Handle different data types
            if isinstance(value, torch.Tensor):
                group.create_dataset(key, data=value.numpy())
            elif isinstance(value, dict):
                # For nested dictionaries like structured_zones, convert to string
                group.create_dataset(key, data=str(value))
            else:
                group.create_dataset(key, data=value)

        # Close current file writer
        h5_writer.close()
        print(f"{case_name} mesh has been written")


if __name__ == "__main__":
    import multiprocessing

    # For debugging
    debug_file_path = "mesh_example/Tri_structured_mixed_mesh_farfield/Tri_structured_mixed_mesh_farfield.cgns"

    path = {
        "simulator": "CGNS",
        "cgns_dataset_path": "mesh_example/Tri_structured_mixed_mesh_farfield",
        "mesh_only": True,
    }

    if debug_file_path and os.path.exists(debug_file_path):
        # Debug mode - process single file
        print(f"Debug mode: processing {debug_file_path}")

        file_name = os.path.basename(debug_file_path)
        file_dir = os.path.dirname(debug_file_path)
        case_name = os.path.basename(file_dir)
        path["file_dir"] = file_dir
        path["case_name"] = case_name
        path["file_name"] = file_name

        data = CGNS_manager(
            mesh_file=debug_file_path,
            data_file=None,
            file_dir=file_dir,
            case_name=case_name,
            path=path,
        )

        h5_dataset = data.extract_mesh(mesh_only=path["mesh_only"])

        # Write to H5 file
        os.makedirs(file_dir, exist_ok=True)
        h5_writer = h5py.File(f"{file_dir}/{case_name}.h5", "w")

        group = h5_writer.create_group(case_name)
        for key, value in h5_dataset.items():
            if isinstance(value, torch.Tensor):
                group.create_dataset(key, data=value.numpy())
            elif isinstance(value, dict):
                group.create_dataset(key, data=str(value))
            else:
                group.create_dataset(key, data=value)

        h5_writer.close()
        print(f"{case_name} mesh has been written to {file_dir}/{case_name}.h5")
    else:
        print("Debug file not found, please check the path")

    print("Done")
