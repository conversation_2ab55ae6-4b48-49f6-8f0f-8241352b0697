"""
集成适配器 - 将统一框架与现有项目集成
"""
import torch
from typing import Dict, Any, Optional
from .unified_interface import UnifiedCFDSolver, create_unstructured_solver
from .unified_discretization import FiniteVolumeDiscretization

class ProjectIntegrationAdapter:
    """项目集成适配器"""
    
    def __init__(self, existing_integrator):
        """
        Args:
            existing_integrator: 现有项目的FVscheme积分器
        """
        self.existing_integrator = existing_integrator
        self.unified_solver = None
    
    def create_unified_solver_from_graphs(self, graph_node, graph_edge, graph_cell):
        """从现有图数据创建统一求解器"""
        # 提取网格数据
        mesh_data = {
            'cells_node': graph_node.face,
            'cells_face': graph_edge.face, 
            'cells_index': graph_cell.face,
            'face_node': graph_edge.edge_index,
            'mesh_pos': graph_node.pos,
            'face_area': graph_edge.face_area,
            'cells_area': graph_cell.cells_area
        }
        
        self.unified_solver = create_unstructured_solver(mesh_data)
        return self.unified_solver
    
    def compare_discretization_methods(self, uvp_node, graph_node, graph_edge, graph_cell, graph_Index, params):
        """比较现有方法和统一框架的结果"""
        
        # 1. 使用现有方法
        existing_result = self.existing_integrator(
            uvp_new_node=uvp_node,
            uv_hat_node=uvp_node[:, 0:2],
            uv_old_node=uvp_node[:, 0:2],
            graph_node=graph_node,
            graph_node_x=None,
            graph_edge=graph_edge,
            graph_cell=graph_cell,
            graph_Index=graph_Index,
            params=params
        )
        
        # 2. 使用统一框架 (如果已创建)
        if self.unified_solver is not None:
            # 转换数据格式
            Q = self._convert_to_conservative_variables(uvp_node)
            unified_result = self.unified_solver.solve_step(Q)
            
            # 比较结果
            comparison = self._compare_results(existing_result, unified_result)
            return existing_result, unified_result, comparison
        
        return existing_result, None, None
    
    def _convert_to_conservative_variables(self, uvp_node):
        """转换为守恒变量"""
        # uvp_node: [u, v, p] -> Q: [rho, rho*u, rho*v, rho*E]
        rho = torch.ones_like(uvp_node[:, 0:1])  # 假设密度为1
        rho_u = rho * uvp_node[:, 0:1]
        rho_v = rho * uvp_node[:, 1:2]
        rho_E = uvp_node[:, 2:3] / 0.4 + 0.5 * rho * (uvp_node[:, 0:1]**2 + uvp_node[:, 1:2]**2)
        
        Q = torch.cat([rho, rho_u, rho_v, rho_E], dim=1)
        return Q
    
    def _compare_results(self, existing_result, unified_result):
        """比较两种方法的结果"""
        if existing_result is None or unified_result is None:
            return None
        
        # 提取可比较的部分
        existing_residual = existing_result[1] if len(existing_result) > 1 else existing_result[0]
        
        # 计算差异
        if isinstance(existing_residual, torch.Tensor) and isinstance(unified_result, torch.Tensor):
            if existing_residual.shape == unified_result.shape:
                diff = torch.norm(existing_residual - unified_result)
                relative_diff = diff / torch.norm(existing_residual)
                
                return {
                    'absolute_difference': diff.item(),
                    'relative_difference': relative_diff.item(),
                    'max_difference': torch.max(torch.abs(existing_residual - unified_result)).item()
                }
        
        return {'error': 'Shape mismatch or invalid data types'}

class EnhancedFiniteVolumeDiscretization(FiniteVolumeDiscretization):
    """增强的有限体积离散化 - 集成现有项目的方法"""
    
    def __init__(self, mesh, existing_methods=None):
        super().__init__(mesh)
        self.existing_methods = existing_methods or {}
    
    def compute_gradient(self, phi: torch.Tensor) -> torch.Tensor:
        """使用现有项目的WLSQ梯度重构"""
        if 'wlsq_gradient' in self.existing_methods:
            return self.existing_methods['wlsq_gradient'](phi)
        else:
            return super().compute_gradient(phi)
    
    def compute_flux(self, phi: torch.Tensor, grad_phi: torch.Tensor) -> torch.Tensor:
        """使用现有项目的通量计算"""
        if 'flux_computation' in self.existing_methods:
            return self.existing_methods['flux_computation'](phi, grad_phi)
        else:
            return super().compute_flux(phi, grad_phi)
    
    def compute_residual(self, flux: torch.Tensor) -> torch.Tensor:
        """使用现有项目的残差聚合"""
        if 'residual_computation' in self.existing_methods:
            return self.existing_methods['residual_computation'](flux)
        else:
            return super().compute_residual(flux)

def create_hybrid_solver(graph_node, graph_edge, graph_cell, existing_integrator):
    """创建混合求解器 - 结合现有方法和统一框架"""
    
    # 提取现有方法
    existing_methods = {
        'wlsq_gradient': lambda phi: existing_integrator.node_to_face_2nd_order(
            node_phi=phi, node_grad=None, graph_node=graph_node, graph_edge=graph_edge
        ),
        # 可以添加更多现有方法
    }
    
    # 创建网格
    mesh_data = {
        'cells_node': graph_node.face,
        'cells_face': graph_edge.face,
        'cells_index': graph_cell.face,
        'face_node': graph_edge.edge_index,
        'mesh_pos': graph_node.pos,
        'face_area': graph_edge.face_area,
        'cells_area': graph_cell.cells_area
    }
    
    # 创建增强的求解器
    from .unified_mesh import MeshFactory
    mesh = MeshFactory.create_unstructured_mesh(**mesh_data)
    
    # 替换默认的离散化方法
    enhanced_discretization = EnhancedFiniteVolumeDiscretization(mesh, existing_methods)
    
    # 创建统一求解器
    unified_solver = UnifiedCFDSolver(mesh_data)
    unified_solver.solver.discretization = enhanced_discretization
    
    return unified_solver

def benchmark_methods(graph_node, graph_edge, graph_cell, graph_Index, params, existing_integrator):
    """基准测试 - 比较不同方法的性能"""
    import time
    
    # 创建测试数据
    n_nodes = graph_node.pos.shape[0]
    uvp_test = torch.randn(n_nodes, 3)
    
    results = {}
    
    # 1. 测试现有方法
    start_time = time.time()
    try:
        existing_result = existing_integrator(
            uvp_new_node=uvp_test,
            uv_hat_node=uvp_test[:, 0:2],
            uv_old_node=uvp_test[:, 0:2],
            graph_node=graph_node,
            graph_node_x=None,
            graph_edge=graph_edge,
            graph_cell=graph_cell,
            graph_Index=graph_Index,
            params=params
        )
        existing_time = time.time() - start_time
        results['existing'] = {
            'time': existing_time,
            'result_shape': [r.shape if hasattr(r, 'shape') else str(type(r)) for r in existing_result],
            'success': True
        }
    except Exception as e:
        results['existing'] = {'time': 0, 'error': str(e), 'success': False}
    
    # 2. 测试统一框架
    start_time = time.time()
    try:
        unified_solver = create_hybrid_solver(graph_node, graph_edge, graph_cell, existing_integrator)
        Q_test = torch.randn(graph_cell.pos.shape[0], 4)  # 守恒变量
        unified_result = unified_solver.solve_step(Q_test)
        unified_time = time.time() - start_time
        results['unified'] = {
            'time': unified_time,
            'result_shape': unified_result.shape,
            'success': True
        }
    except Exception as e:
        results['unified'] = {'time': 0, 'error': str(e), 'success': False}
    
    # 3. 性能比较
    if results['existing']['success'] and results['unified']['success']:
        speedup = results['existing']['time'] / results['unified']['time']
        results['comparison'] = {
            'speedup': speedup,
            'existing_faster': speedup > 1.0
        }
    
    return results

# 使用示例
def integration_example():
    """集成示例"""
    print("=== 项目集成示例 ===")
    
    # 假设我们有现有的图数据和积分器
    # graph_node, graph_edge, graph_cell = load_existing_data()
    # existing_integrator = load_existing_integrator()
    
    # 创建适配器
    # adapter = ProjectIntegrationAdapter(existing_integrator)
    
    # 创建统一求解器
    # unified_solver = adapter.create_unified_solver_from_graphs(
    #     graph_node, graph_edge, graph_cell
    # )
    
    # 比较方法
    # uvp_test = torch.randn(100, 3)
    # existing_result, unified_result, comparison = adapter.compare_discretization_methods(
    #     uvp_test, graph_node, graph_edge, graph_cell, graph_Index, params
    # )
    
    # print(f"方法比较结果: {comparison}")
    
    print("集成适配器已准备就绪，等待实际数据测试")

if __name__ == "__main__":
    integration_example()
