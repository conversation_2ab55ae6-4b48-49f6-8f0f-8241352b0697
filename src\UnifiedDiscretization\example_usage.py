"""
统一离散化框架使用示例
演示如何在同一套代码中处理结构网格和非结构网格
"""
import torch
import numpy as np
import matplotlib.pyplot as plt
from .unified_interface import create_structured_solver, create_unstructured_solver

def cavity_flow_structured():
    """方腔流 - 结构网格求解"""
    print("=== 方腔流 - 结构网格 ===")
    
    # 网格参数
    ni, nj = 64, 64
    L = 1.0
    dx = L / (ni - 1)
    dy = L / (nj - 1)
    
    # 创建求解器
    solver = create_structured_solver(ni, nj, dx, dy)
    
    # 初始化流场 (静止流体)
    total_cells = ni * nj
    Q = torch.zeros(total_cells, 4)
    Q[:, 0] = 1.0      # 密度
    Q[:, 1] = 0.0      # x动量
    Q[:, 2] = 0.0      # y动量
    Q[:, 3] = 2.5      # 总能量
    
    # 设置边界条件 (上壁面运动)
    def apply_boundary_conditions(Q_field, ni, nj):
        """应用边界条件"""
        Q_bc = Q_field.clone()
        
        # 上壁面 (j = nj-1): u = 1.0, v = 0.0
        for i in range(ni):
            cell_id = i * nj + (nj - 1)
            Q_bc[cell_id, 1] = 1.0  # rho*u = 1.0 (假设rho=1)
            Q_bc[cell_id, 2] = 0.0  # rho*v = 0.0
        
        # 其他壁面: u = 0.0, v = 0.0
        # 左壁面 (i = 0)
        for j in range(nj):
            cell_id = 0 * nj + j
            Q_bc[cell_id, 1] = 0.0
            Q_bc[cell_id, 2] = 0.0
        
        # 右壁面 (i = ni-1)
        for j in range(nj):
            cell_id = (ni - 1) * nj + j
            Q_bc[cell_id, 1] = 0.0
            Q_bc[cell_id, 2] = 0.0
        
        # 下壁面 (j = 0)
        for i in range(ni):
            cell_id = i * nj + 0
            Q_bc[cell_id, 1] = 0.0
            Q_bc[cell_id, 2] = 0.0
        
        return Q_bc
    
    # 时间推进
    max_iter = 500
    for iter_count in range(max_iter):
        Q_old = Q.clone()
        Q = solver.solve_step(Q)
        Q = apply_boundary_conditions(Q, ni, nj)
        
        if iter_count % 50 == 0:
            residual = torch.norm(Q - Q_old)
            print(f"第 {iter_count} 步, 残差: {residual:.2e}")
    
    # 提取速度场
    u = Q[:, 1] / Q[:, 0]  # u = rho*u / rho
    v = Q[:, 2] / Q[:, 0]  # v = rho*v / rho
    
    # 重塑为二维数组用于可视化
    u_2d = u.reshape(ni, nj).numpy()
    v_2d = v.reshape(ni, nj).numpy()
    
    return u_2d, v_2d, solver.get_mesh_info()

def cylinder_flow_unstructured():
    """圆柱绕流 - 非结构网格求解"""
    print("=== 圆柱绕流 - 非结构网格 ===")
    
    # 生成简化的圆柱绕流网格
    def generate_cylinder_mesh():
        """生成简化的圆柱绕流网格"""
        # 这里用简化的网格代替真实的圆柱网格
        n_cells = 200
        n_faces = 400
        n_nodes = 250
        
        # 随机生成网格数据 (实际应用中应使用真实的网格生成器)
        mesh_data = {
            'cells_node': torch.randint(0, n_faces, (n_cells * 4,)),
            'cells_face': torch.randint(0, n_faces, (n_cells * 4,)),
            'cells_index': torch.repeat_interleave(torch.arange(n_cells), 4),
            'face_node': torch.randint(0, n_nodes, (2, n_faces)),
            'mesh_pos': torch.randn(n_nodes, 2) * 2.0,  # 扩大计算域
            'face_area': torch.rand(n_faces, 1) * 0.1,
            'cells_area': torch.rand(n_cells, 1) * 0.05
        }
        
        return mesh_data, n_cells
    
    mesh_data, n_cells = generate_cylinder_mesh()
    
    # 创建求解器
    solver = create_unstructured_solver(mesh_data)
    
    # 初始化流场
    Q = torch.zeros(n_cells, 4)
    Q[:, 0] = 1.0      # 密度
    Q[:, 1] = 0.5      # x动量 (来流速度)
    Q[:, 2] = 0.0      # y动量
    Q[:, 3] = 2.5      # 总能量
    
    # 简化的边界条件处理
    def apply_cylinder_bc(Q_field):
        """应用圆柱边界条件"""
        Q_bc = Q_field.clone()
        
        # 简化处理：假设前10%的单元是圆柱表面
        n_cylinder_cells = int(0.1 * n_cells)
        Q_bc[:n_cylinder_cells, 1] = 0.0  # 圆柱表面无滑移
        Q_bc[:n_cylinder_cells, 2] = 0.0
        
        return Q_bc
    
    # 时间推进
    max_iter = 300
    for iter_count in range(max_iter):
        Q_old = Q.clone()
        Q = solver.solve_step(Q)
        Q = apply_cylinder_bc(Q)
        
        if iter_count % 30 == 0:
            residual = torch.norm(Q - Q_old)
            print(f"第 {iter_count} 步, 残差: {residual:.2e}")
    
    # 提取结果
    u = Q[:, 1] / Q[:, 0]
    v = Q[:, 2] / Q[:, 0]
    
    return u.numpy(), v.numpy(), solver.get_mesh_info()

def compare_discretization_methods():
    """比较两种离散化方法"""
    print("=== 离散化方法比较 ===")
    
    # 1. 结构网格方腔流
    print("求解结构网格方腔流...")
    u_struct, v_struct, info_struct = cavity_flow_structured()
    
    # 2. 非结构网格圆柱绕流
    print("求解非结构网格圆柱绕流...")
    u_unstruct, v_unstruct, info_unstruct = cylinder_flow_unstructured()
    
    # 3. 结果比较
    print("\n=== 方法比较结果 ===")
    print(f"结构网格信息: {info_struct}")
    print(f"非结构网格信息: {info_unstruct}")
    
    print(f"\n结构网格速度范围:")
    print(f"  u: {u_struct.min():.3f} ~ {u_struct.max():.3f}")
    print(f"  v: {v_struct.min():.3f} ~ {v_struct.max():.3f}")
    
    print(f"\n非结构网格速度范围:")
    print(f"  u: {u_unstruct.min():.3f} ~ {u_unstruct.max():.3f}")
    print(f"  v: {v_unstruct.min():.3f} ~ {v_unstruct.max():.3f}")
    
    return {
        'structured': {'u': u_struct, 'v': v_struct, 'info': info_struct},
        'unstructured': {'u': u_unstruct, 'v': v_unstruct, 'info': info_unstruct}
    }

def visualize_results(results):
    """可视化结果"""
    try:
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 结构网格结果
        u_struct = results['structured']['u']
        v_struct = results['structured']['v']
        
        # 速度幅值
        vel_mag_struct = np.sqrt(u_struct**2 + v_struct**2)
        
        im1 = axes[0, 0].contourf(u_struct, levels=20, cmap='RdBu_r')
        axes[0, 0].set_title('结构网格 - u速度')
        plt.colorbar(im1, ax=axes[0, 0])
        
        im2 = axes[0, 1].contourf(v_struct, levels=20, cmap='RdBu_r')
        axes[0, 1].set_title('结构网格 - v速度')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # 非结构网格结果 (散点图)
        u_unstruct = results['unstructured']['u']
        v_unstruct = results['unstructured']['v']
        
        scatter1 = axes[1, 0].scatter(range(len(u_unstruct)), u_unstruct, 
                                    c=u_unstruct, cmap='RdBu_r', s=1)
        axes[1, 0].set_title('非结构网格 - u速度')
        plt.colorbar(scatter1, ax=axes[1, 0])
        
        scatter2 = axes[1, 1].scatter(range(len(v_unstruct)), v_unstruct, 
                                    c=v_unstruct, cmap='RdBu_r', s=1)
        axes[1, 1].set_title('非结构网格 - v速度')
        plt.colorbar(scatter2, ax=axes[1, 1])
        
        plt.tight_layout()
        plt.savefig('unified_discretization_results.png', dpi=150, bbox_inches='tight')
        print("结果已保存到 unified_discretization_results.png")
        
    except ImportError:
        print("matplotlib未安装，跳过可视化")

def performance_benchmark():
    """性能基准测试"""
    print("=== 性能基准测试 ===")
    
    import time
    
    # 测试不同规模的网格
    test_cases = [
        {'ni': 32, 'nj': 32, 'n_cells_unstruct': 100},
        {'ni': 64, 'nj': 64, 'n_cells_unstruct': 400},
        {'ni': 128, 'nj': 128, 'n_cells_unstruct': 1600},
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n测试规模: {case['ni']}x{case['nj']} (结构), {case['n_cells_unstruct']} (非结构)")
        
        # 结构网格测试
        start_time = time.time()
        solver_struct = create_structured_solver(case['ni'], case['nj'], 0.01, 0.01)
        Q_struct = torch.ones(case['ni'] * case['nj'], 4)
        Q_struct_result = solver_struct.solve_step(Q_struct)
        struct_time = time.time() - start_time
        
        # 非结构网格测试
        start_time = time.time()
        n_cells = case['n_cells_unstruct']
        mesh_data = {
            'cells_node': torch.randint(0, n_cells*2, (n_cells * 3,)),
            'cells_face': torch.randint(0, n_cells*2, (n_cells * 3,)),
            'cells_index': torch.repeat_interleave(torch.arange(n_cells), 3),
            'face_node': torch.randint(0, n_cells, (2, n_cells*2)),
            'mesh_pos': torch.randn(n_cells, 2),
            'face_area': torch.rand(n_cells*2, 1),
            'cells_area': torch.rand(n_cells, 1)
        }
        solver_unstruct = create_unstructured_solver(mesh_data)
        Q_unstruct = torch.ones(n_cells, 4)
        Q_unstruct_result = solver_unstruct.solve_step(Q_unstruct)
        unstruct_time = time.time() - start_time
        
        results.append({
            'scale': f"{case['ni']}x{case['nj']}",
            'struct_time': struct_time,
            'unstruct_time': unstruct_time,
            'speedup': struct_time / unstruct_time if unstruct_time > 0 else float('inf')
        })
        
        print(f"  结构网格时间: {struct_time:.4f}s")
        print(f"  非结构网格时间: {unstruct_time:.4f}s")
        print(f"  加速比: {results[-1]['speedup']:.2f}")
    
    return results

if __name__ == "__main__":
    print("统一离散化框架演示")
    print("=" * 50)
    
    # 1. 比较离散化方法
    results = compare_discretization_methods()
    
    # 2. 可视化结果
    visualize_results(results)
    
    # 3. 性能测试
    perf_results = performance_benchmark()
    
    print("\n=== 总结 ===")
    print("✓ 成功实现了统一的编程逻辑")
    print("✓ 支持结构网格有限差分和非结构网格有限体积")
    print("✓ 提供了一致的求解接口")
    print("✓ 便于方法比较和性能分析")
    
    print(f"\n性能测试结果:")
    for result in perf_results:
        print(f"  {result['scale']}: 结构网格 {result['struct_time']:.4f}s, "
              f"非结构网格 {result['unstruct_time']:.4f}s, 加速比 {result['speedup']:.2f}")
