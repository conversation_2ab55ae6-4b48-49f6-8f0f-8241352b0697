"""
统一离散化框架
支持结构网格有限差分和非结构网格有限体积的统一编程逻辑
"""

from .unified_mesh import (
    UnifiedMeshBase,
    StructuredMesh, 
    UnstructuredMesh,
    MeshFactory,
    MeshType,
    DiscretizationMethod
)

from .unified_discretization import (
    UnifiedDiscretization,
    FiniteDifferenceDiscretization,
    FiniteVolumeDiscretization,
    DiscretizationFactory,
    UnifiedSolver
)

from .unified_interface import (
    UnifiedCFDSolver,
    create_structured_solver,
    create_unstructured_solver,
    load_solver_from_existing_project
)

from .integration_adapter import (
    ProjectIntegrationAdapter,
    EnhancedFiniteVolumeDiscretization,
    create_hybrid_solver,
    benchmark_methods
)

__version__ = "1.0.0"
__author__ = "CFD Team"
__description__ = "统一离散化框架 - 支持结构网格和非结构网格的统一处理"

# 便捷导入
__all__ = [
    # 核心类
    'UnifiedCFDSolver',
    'UnifiedSolver', 
    'MeshFactory',
    'DiscretizationFactory',
    
    # 网格类型
    'MeshType',
    'DiscretizationMethod',
    'StructuredMesh',
    'UnstructuredMesh',
    
    # 离散化方法
    'FiniteDifferenceDiscretization',
    'FiniteVolumeDiscretization',
    
    # 便捷函数
    'create_structured_solver',
    'create_unstructured_solver',
    'load_solver_from_existing_project',
    'create_hybrid_solver',
    
    # 集成工具
    'ProjectIntegrationAdapter',
    'benchmark_methods',
]

def quick_start_structured(ni=50, nj=30, dx=0.02, dy=0.02):
    """快速开始 - 结构网格"""
    return create_structured_solver(ni, nj, dx, dy)

def quick_start_unstructured(mesh_data):
    """快速开始 - 非结构网格"""
    return create_unstructured_solver(mesh_data)

# 版本信息
def get_version_info():
    """获取版本信息"""
    return {
        'version': __version__,
        'author': __author__,
        'description': __description__,
        'supported_methods': [
            'Structured Grid + Finite Difference',
            'Unstructured Grid + Finite Volume'
        ],
        'features': [
            'Unified Programming Interface',
            'Method Comparison Tools', 
            'Performance Benchmarking',
            'Integration with Existing Code'
        ]
    }

# 打印欢迎信息
def print_welcome():
    """打印欢迎信息"""
    info = get_version_info()
    print("=" * 60)
    print(f"  {info['description']}")
    print(f"  版本: {info['version']}")
    print("=" * 60)
    print("支持的方法:")
    for method in info['supported_methods']:
        print(f"  ✓ {method}")
    print("\n主要特性:")
    for feature in info['features']:
        print(f"  ✓ {feature}")
    print("=" * 60)

# 使用示例
USAGE_EXAMPLES = """
使用示例:

1. 结构网格求解:
   from UnifiedDiscretization import create_structured_solver
   solver = create_structured_solver(ni=100, nj=50, dx=0.01, dy=0.01)
   Q = torch.ones(5000, 4)  # 初始化
   result = solver.solve_step(Q)

2. 非结构网格求解:
   from UnifiedDiscretization import create_unstructured_solver
   solver = create_unstructured_solver(mesh_data)
   Q = torch.ones(n_cells, 4)
   result = solver.solve_step(Q)

3. 与现有项目集成:
   from UnifiedDiscretization import load_solver_from_existing_project
   solver = load_solver_from_existing_project(graph_node, graph_edge, graph_cell)

4. 性能比较:
   from UnifiedDiscretization import benchmark_methods
   results = benchmark_methods(graph_node, graph_edge, graph_cell, 
                              graph_Index, params, existing_integrator)
"""

def print_usage():
    """打印使用示例"""
    print(USAGE_EXAMPLES)

# 自动检测和建议
def auto_detect_and_suggest(data):
    """自动检测数据类型并给出建议"""
    suggestions = []
    
    if isinstance(data, dict):
        if 'ni' in data and 'nj' in data:
            suggestions.append("检测到结构网格参数，建议使用 create_structured_solver()")
        elif 'cells_face' in data and 'face_node' in data:
            suggestions.append("检测到非结构网格数据，建议使用 create_unstructured_solver()")
        else:
            suggestions.append("无法识别数据类型，请检查输入格式")
    
    return suggestions

# 调试和诊断工具
def diagnose_mesh(mesh_data):
    """诊断网格数据"""
    diagnosis = {
        'data_type': type(mesh_data).__name__,
        'keys': list(mesh_data.keys()) if isinstance(mesh_data, dict) else 'Not a dict',
        'suggestions': auto_detect_and_suggest(mesh_data),
        'potential_issues': []
    }
    
    if isinstance(mesh_data, dict):
        # 检查常见问题
        if 'cells_face' in mesh_data and 'cells_index' in mesh_data:
            cells_face = mesh_data['cells_face']
            cells_index = mesh_data['cells_index']
            if hasattr(cells_face, 'shape') and hasattr(cells_index, 'shape'):
                if cells_face.shape != cells_index.shape:
                    diagnosis['potential_issues'].append(
                        f"cells_face和cells_index形状不匹配: {cells_face.shape} vs {cells_index.shape}"
                    )
    
    return diagnosis
