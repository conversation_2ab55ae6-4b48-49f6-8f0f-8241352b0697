{"file_info": {"file_path": "mesh_example/Tri_structured_mixed_mesh_farfield/Tri_structured_mixed_mesh_farfield.cgns", "file_size_bytes": 1496434, "analysis_timestamp": "{\"timestamp\": \"generated_at_runtime\"}"}, "summary": {"zones": {"dom-4": {"zone_type": "Unstructured", "zone_dimensions": [3, 1], "coordinates": {"CoordinateX": {"shape": [17598], "data_type": "float64"}, "CoordinateY": {"shape": [17598], "data_type": "float64"}, "CoordinateZ": {"shape": [17598], "data_type": "float64"}}, "boundary_conditions": {"inlet": {"grid_location": "EdgeCenter", "point_range": [[34570], [34656]], "point_range_shape": [2, 1], "family_name": "inlet"}, "outlet": {"grid_location": "EdgeCenter", "point_range": [[34657], [35077]], "point_range_shape": [2, 1], "family_name": "outlet"}}, "grid_connectivity": ["1to1Connection:con-29-split-1", "1to1Connection:con-29-split-2"]}, "dom-5": {"zone_type": "Structured", "zone_dimensions": [3, 2], "coordinates": {"CoordinateX": {"shape": [60, 32], "data_type": "float64"}, "CoordinateY": {"shape": [60, 32], "data_type": "float64"}, "CoordinateZ": {"shape": [60, 32], "data_type": "float64"}}, "boundary_conditions": {"con-27-split-2": {"grid_location": "Vertex", "point_range": [[1, 1], [1, 60]], "point_range_shape": [2, 2], "family_name": "wall"}}, "grid_connectivity": ["1to1ConnectionA1", "1to1ConnectionA2", "1to1Connection:con-29-split-1"]}, "dom-6": {"zone_type": "Structured", "zone_dimensions": [3, 2], "coordinates": {"CoordinateX": {"shape": [61, 32], "data_type": "float64"}, "CoordinateY": {"shape": [61, 32], "data_type": "float64"}, "CoordinateZ": {"shape": [61, 32], "data_type": "float64"}}, "boundary_conditions": {"con-27-split-1": {"grid_location": "Vertex", "point_range": [[1, 1], [1, 61]], "point_range_shape": [2, 2], "family_name": "wall"}}, "grid_connectivity": ["1to1ConnectionB1", "1to1ConnectionB2", "1to1Connection:con-29-split-2"]}}, "boundary_conditions": {}, "grid_connectivity": {}, "families": {"inlet": {"bc_type": "BCInflow", "is_standard_bc": true, "bc_type_name": "Inflow"}, "outlet": {"bc_type": "BCOutflow", "is_standard_bc": true, "bc_type_name": "Outflow"}, "wall": {"bc_type": "BCWall", "is_standard_bc": true, "bc_type_name": "Wall"}}}}